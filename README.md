# P2P Chess Game

A peer-to-peer multiplayer chess game built with PeerJS. No server required for gameplay!

## Features

- **Peer-to-Peer Multiplayer**: Direct connection between players using WebRTC
- **No Server Required**: Games run entirely in the browser
- **Full Chess Rules**: Includes castling, en passant, promotion, check, checkmate, and stalemate
- **Timer Support**: 10-minute games with visual timers
- **Mobile Friendly**: Touch-optimized interface for mobile devices
- **Move History**: Complete game notation and captured pieces display
- **Reconnection**: Automatic reconnection attempts if connection is lost

## How to Play

### Starting a Game

1. **Host a Game**:
   - Click "Wait for Player" 
   - Share your Peer ID with a friend
   - Wait for them to connect

2. **Join a Game**:
   - Get the Peer ID from your friend
   - Enter it in the "Enter friend's ID" field
   - Click "Join Game"

### Game Rules

- **White** always goes first
- **Host** plays as White, **Guest** plays as Black
- Click or drag pieces to move
- The game follows standard chess rules
- Timer starts at 10 minutes per player

### Controls

- **Desktop**: Drag and drop pieces or click to select and move
- **Mobile**: Tap to select pieces, then tap destination square
- **Copy ID**: Click the copy button next to your ID to share it easily

## Technical Details

### Requirements

- Modern web browser with WebRTC support
- Internet connection (for initial peer discovery)
- No server installation required

### Running Locally

1. Serve the files using any HTTP server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```

2. Open `http://localhost:8000/chess.html` in your browser

### Architecture

- **Frontend**: HTML5, CSS3, JavaScript
- **Chess Logic**: chess.js library
- **Board Display**: chessboard.js library
- **P2P Communication**: PeerJS (WebRTC wrapper)
- **No Backend**: Fully client-side application

### Browser Compatibility

- Chrome 56+
- Firefox 52+
- Safari 11+
- Edge 79+

## Troubleshooting

### Connection Issues

1. **"Failed to initialize peer"**: 
   - Check internet connection
   - Try refreshing the page
   - Ensure WebRTC is enabled in browser

2. **"Connection failed"**:
   - Verify the Peer ID is correct
   - Check if both players are online
   - Try switching who hosts/joins

3. **"Connection lost during game"**:
   - Use the "Try Reconnect" button
   - If reconnection fails, return to lobby and start new game

### Firewall/Network Issues

- Some corporate networks block WebRTC
- Try using a different network or mobile hotspot
- VPN might interfere with peer connections

## Development

### File Structure

```
├── chess.html          # Main game interface
├── chess.css           # Game styling
├── chess.js            # Main game logic
├── p2p-chess.js        # P2P connection management
└── README.md           # This file
```

### Key Components

- **P2PChessManager**: Handles peer connections and messaging
- **Chess Game Logic**: Move validation, game state, timers
- **UI Management**: Board display, status updates, popups

## Credits

- Built with ❤️ by Jangra Gitesh
- Chess logic: [chess.js](https://github.com/jhlywa/chess.js)
- Board display: [chessboard.js](https://chessboardjs.com/)
- P2P networking: [PeerJS](https://peerjs.com/)

## License

This project is open source and available under the MIT License.
