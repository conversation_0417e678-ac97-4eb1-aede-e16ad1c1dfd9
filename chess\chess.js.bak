/**
 * Two-Games Chess - Single Player and Multiplayer modes
 */
document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements - Mode Selection
    const modeSelector = document.getElementById('mode-selector');
    const singlePlayerBtn = document.getElementById('single-player-btn');
    const multiplayerBtn = document.getElementById('multiplayer-btn');
    const singlePlayerMode = document.getElementById('single-player-mode');
    const multiplayerMode = document.getElementById('multiplayerMode');
    
    // Mobile detection - Use real detection instead of forcing mobile mode
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    //const isMobile = true; // Force mobile mode for testing
    console.log("Mobile device detected:", isMobile);
    
    // Selected piece tracking for mobile tap interface
    let selectedSquare = null;
    let selectedPiece = null;
    
    // Prevent scrolling when dragging on chessboard
    const preventScrollOnChessboard = () => {
        const gameBoard = document.getElementById('game-board');
        const mpGameBoard = document.getElementById('mp-game-board');
        
        if (gameBoard) {
            gameBoard.addEventListener('touchmove', (e) => {
                e.preventDefault();
            }, { passive: false });
            
            // Add touch handling for mobile devices
            if (isMobile) {
                console.log("Adding mobile touch handlers to game board");
                gameBoard.addEventListener('touchstart', onBoardTouchStart, false);
                gameBoard.addEventListener('touchend', onBoardTouchEnd, false);
            }
        }
        
        if (mpGameBoard) {
            mpGameBoard.addEventListener('touchmove', (e) => {
                e.preventDefault();
            }, { passive: false });
            
            // Add touch handling for mobile devices
            if (isMobile) {
                console.log("Adding mobile touch handlers to multiplayer board");
                mpGameBoard.addEventListener('touchstart', onMpBoardTouchStart, false);
                mpGameBoard.addEventListener('touchend', onMpBoardTouchEnd, false);
            }
        }
    };
    
    // Touch event handlers for single player
    function onBoardTouchStart(e) {
        e.preventDefault();
        console.log("Touch start on game board");
    }

    function onBoardTouchEnd(e) {
        e.preventDefault();
        console.log("Touch end on game board");
        
        // Get the touched element
        const touch = e.changedTouches[0];
        const element = document.elementFromPoint(touch.clientX, touch.clientY);
        
        // Get the square from the element
        let square = null;
        
        if (element.classList.contains('square-55d63')) {
            square = element.getAttribute('data-square');
        } else {
            // Try to find the square parent
            const squareElement = element.closest('.square-55d63');
            if (squareElement) {
                square = squareElement.getAttribute('data-square');
            }
        }
        
        if (square) {
            console.log("Touch detected on square:", square);
            if (isPlayerTurn) {
                handleBoardClick(square);
            }
        }
    }

    // Touch event handlers for multiplayer
    function onMpBoardTouchStart(e) {
        e.preventDefault();
        console.log("Touch start on multiplayer board");
    }

    function onMpBoardTouchEnd(e) {
        e.preventDefault();
        console.log("Touch end on multiplayer board");
        
        // Get the touched element
        const touch = e.changedTouches[0];
        const element = document.elementFromPoint(touch.clientX, touch.clientY);
        
        // Get the square from the element
        let square = null;
        
        if (element.classList.contains('square-55d63')) {
            square = element.getAttribute('data-square');
        } else {
            // Try to find the square parent
            const squareElement = element.closest('.square-55d63');
            if (squareElement) {
                square = squareElement.getAttribute('data-square');
            }
        }
        
        if (square) {
            console.log("MP touch detected on square:", square);
            handleMpBoardClick(square);
        }
    }
    
    // Handle start playing button from homepage if directed here
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('start')) {
        // If directed with ?start=single or ?start=multi, auto-start that mode
        const startMode = urlParams.get('start');
        if (startMode === 'single') {
            setTimeout(() => singlePlayerBtn.click(), 500);
        } else if (startMode === 'multi') {
            setTimeout(() => multiplayerBtn.click(), 500);
        }
    }
    
    // Single Player Mode Elements
    const difficultySelector = document.getElementById('difficulty');
    const playWhiteBtn = document.getElementById('play-white');
    const playBlackBtn = document.getElementById('play-black');
    const newGameBtn = document.getElementById('new-game-btn');
    const undoBtn = document.getElementById('undo-btn');
    const exitSingleBtn = document.getElementById('exit-single-btn');
    const statusElement = document.getElementById('status-message');
    const playerTurnElement = document.getElementById('player-turn');
    const aiTurnElement = document.getElementById('ai-turn');
    const moveListElement = document.getElementById('move-list');
    const whiteCapturedElement = document.getElementById('white-captured-pieces');
    const blackCapturedElement = document.getElementById('black-captured-pieces');
    
    // Multiplayer Mode Elements
    const connectionSetup = document.getElementById('connection-setup');
    const createBtn = document.getElementById('createBtn');
    const joinBtn = document.getElementById('joinBtn');
    const createGameSection = document.getElementById('createGameSection');
    const joinGameSection = document.getElementById('joinGameSection');
    const gameCodeDisplay = document.getElementById('gameCodeDisplay');
    const gameCodeInput = document.getElementById('gameCodeInput');
    const copyCodeBtn = document.getElementById('copyCodeBtn');
    const connectBtn = document.getElementById('connectBtn');
    const statusText = document.getElementById('statusText');
    const joinStatusText = document.getElementById('joinStatusText');
    const multiplayerGame = document.getElementById('multiplayer-game');
    const mpStatusMessage = document.getElementById('mp-status');
    const mpPlayerInfo = document.getElementById('mp-player-info');
    const mpPlayerColor = document.getElementById('mp-player-color');
    const resignBtn = document.getElementById('resign-btn');
    const exitMpBtn = document.getElementById('exit-mp-btn');
    const mpMoveListElement = document.getElementById('mp-move-list');
    const mpWhiteCaptured = document.getElementById('mp-white-captured');
    const mpBlackCaptured = document.getElementById('mp-black-captured');
    
    // Global DOM elements - Multiplayer
    const mpBoardContainer = document.querySelector('#mp-game-board') || document.createElement('div');
    // Use existing statusText instead of redefining createStatusText
    // Use existing connectBtn instead of redefining joinGameBtn
    // Use existing exitMpBtn instead of redefining backFromMpBtn
    // Use existing resignBtn instead of redefining mpResignBtn
    
    // Game state variables - Single Player
    let game = new Chess();
    let board = null;
    let engine = null;
    let playerColor = 'w';
    let isPlayerTurn = true;
    let capturedPieces = { w: [], b: [] };
    let moveHistory = [];
    let $boardEl = null;
    let lastMoveSource = null;
    let lastMoveTarget = null;
    
    // Game state variables - Multiplayer
    let mpGame = null;
    let mpBoard = null;
    let peer = null;
    let connection = null;
    let isHost = false;
    let mpPlayerSide = 'w'; // 'w' for white, 'b' for black
    let mpCapturedPieces = { w: [], b: [] };
    let $mpBoardEl = null;
    let mpLastMoveSource = null;
    let mpLastMoveTarget = null;
    
    // Piece theme config
    const pieceTheme = (piece) => {
        return `https://chessboardjs.com/img/chesspieces/alpha/${piece}.png`;
    };
    
    // Mode Selection Event Listeners
    singlePlayerBtn.addEventListener('click', () => {
        modeSelector.style.display = 'none';
        singlePlayerMode.style.display = 'block';
        initializeSinglePlayerMode();
    });
    
    multiplayerBtn.addEventListener('click', () => {
        modeSelector.style.display = 'none';
        multiplayerMode.style.display = 'block';
        // Initialize create game & join game buttons
        createBtn.addEventListener('click', () => {
            connectionSetup.querySelector('.setup-buttons').style.display = 'none';
            createGameSection.style.display = 'block';
            joinGameSection.style.display = 'none';
            statusText.textContent = 'Setting up game...';
            copyCodeBtn.disabled = true;
            initializePeer();
        });
        
        joinBtn.addEventListener('click', () => {
            connectionSetup.querySelector('.setup-buttons').style.display = 'none';
            joinGameSection.style.display = 'block';
            createGameSection.style.display = 'none';
        });
        
        connectBtn.addEventListener('click', () => {
            const code = gameCodeInput.value.trim().toUpperCase();
            if (code && code.length >= 4) {
                joinStatusText.textContent = `Connecting to game ${code}...`;
                connectBtn.disabled = true;
                connectToPeer(code);
            } else {
                joinStatusText.textContent = 'Please enter a valid game code';
            }
        });
    });
    
    // ===== SINGLE PLAYER MODE =====
    
    // Initialize single player mode
    function initializeSinglePlayerMode() {
        // Initialize the board without click handler for mobile
        const boardConfig = {
            position: 'start',
            pieceTheme: pieceTheme,
            draggable: !isMobile,  // Disable dragging on mobile
            onDragStart: onDragStart,
            onDrop: onDrop,
            onSnapEnd: onSnapEnd,
            sparePieces: false,
            showNotation: false,
            snapbackSpeed: 250,
            snapSpeed: 100
        };
        
        $boardEl = $('#game-board');
        board = Chessboard('game-board', boardConfig);
        
        // Initialize the chess engine
        engine = new ChessEngine(game);
        engine.setDifficulty(difficultySelector.value);
        
        // Setup mobile click handling after board is initialized
        setupMobileTapHandling();
        
        // Prevent scrolling on touch devices
        preventScrollOnChessboard();
        
        // Resize handler
        $(window).resize(() => {
            if (board) board.resize();
        });
        
        // Start a new game
        startNewGame();
    }
    
    // Setup mobile tap handling for both boards
    function setupMobileTapHandling() {
        if (isMobile) {
            // Clear any previous handlers
            $('#game-board').off('click', '.square-55d63');
            
            // Add click event directly to all squares
            $('#game-board').on('click', '.square-55d63', function() {
                const square = $(this).attr('data-square');
                console.log("Square clicked:", square);
                if (isPlayerTurn) {
                    handleBoardClick(square);
                }
            });
            
            // Do the same for multiplayer board
            $('#mp-game-board').off('click', '.square-55d63');
            $('#mp-game-board').on('click', '.square-55d63', function() {
                const square = $(this).attr('data-square');
                console.log("MP Square clicked:", square);
                handleMpBoardClick(square);
            });
        }
    }
    
    // Event listeners for single player mode
    difficultySelector.addEventListener('change', () => {
        engine.setDifficulty(difficultySelector.value);
        updateStatus(`Difficulty set to ${difficultySelector.options[difficultySelector.selectedIndex].text}`);
    });
    
    playWhiteBtn.addEventListener('click', () => {
        playWhiteBtn.classList.add('active');
        playBlackBtn.classList.remove('active');
        if (playerColor !== 'w') {
            playerColor = 'w';
            startNewGame();
        }
    });
    
    playBlackBtn.addEventListener('click', () => {
        playBlackBtn.classList.add('active');
        playWhiteBtn.classList.remove('active');
        if (playerColor !== 'b') {
            playerColor = 'b';
            startNewGame();
        }
    });
    
    newGameBtn.addEventListener('click', startNewGame);
    
    undoBtn.addEventListener('click', () => {
        if (game.history().length >= 2) {
            game.undo(); // Undo AI's move
            game.undo(); // Undo player's move
            board.position(game.fen());
            capturedPieces = calculateCapturedPieces(game);
            updateCapturedPieces();
            updateMoveList();
            isPlayerTurn = true;
            updateTurnIndicator();
            updateStatus("Move undone. Your turn.");
        }
    });
    
    exitSingleBtn.addEventListener('click', () => {
        singlePlayerMode.style.display = 'none';
        modeSelector.style.display = 'block';
        resetSinglePlayerGame();
    });
    
    // Functions for single player mode
    function startNewGame() {
        game = new Chess();
        board.position(game.fen());
        
        capturedPieces = { w: [], b: [] };
        updateCapturedPieces();
        
        moveHistory = [];
        updateMoveList();
        
        isPlayerTurn = playerColor === 'w';
        updateTurnIndicator();
        
        updateStatus(isPlayerTurn ? "Your turn" : "AI is thinking...");
        
        // Prevent scrolling on touch devices
        preventScrollOnChessboard();
        
        if (!isPlayerTurn) {
            // If player is black, AI (white) moves first
            setTimeout(makeAIMove, 500);
        }
    }
    
    function resetSinglePlayerGame() {
        game = new Chess();
        if (board) {
            board.position(game.fen());
        }
        capturedPieces = { w: [], b: [] };
        moveHistory = [];
        isPlayerTurn = true;
    }
    
    function onDragStart(source, piece) {
        // Don't allow drag if not player's turn
        if (!isPlayerTurn) return false;
        
        // Only allow player to move their own pieces
        if ((playerColor === 'w' && piece.search(/^b/) !== -1) ||
            (playerColor === 'b' && piece.search(/^w/) !== -1)) {
            return false;
        }
        
        // Highlight valid target squares if on desktop
        if (!isMobile) {
            highlightValidMoves(source);
        }
        
        return true;
    }
    
    function onDrop(source, target, piece, newPos, oldPos, orientation) {
        // Remove highlights
        removeHighlighting();
        
        // See if the move is legal
        const move = game.move({
            from: source,
            to: target,
            promotion: 'q' // Always promote to queen for simplicity
        });
        
        // Illegal move
        if (move === null) return 'snapback';
        
        // Check for captures
        checkForCapture(move);
        
        // Highlight last move
        highlightLastMove(source, target);
        lastMoveSource = source;
        lastMoveTarget = target;
        
        // Add move to history and update game status
        addMoveToHistory(move);
        updateMoveList();
        updateGameStatus();
        
        // Update turn indicator
        updateTurnIndicator();
        
        // Start AI thinking
        isPlayerTurn = false;
        setTimeout(makeAIMove, 500);
    }
    
    function onSnapEnd() {
        board.position(game.fen());
    }
    
    function makeAIMove() {
        if (game.game_over()) {
            isPlayerTurn = false;
            updateTurnIndicator();
            updateGameStatus();
            return;
        }
        
        // Get best move from engine
        const aiMove = engine.getBestMove(game, playerColor === 'w' ? 'b' : 'w');
        
        if (aiMove) {
            // Highlight source square before move
            highlightSquare(aiMove.from);
            
            // Make the AI move
            const result = game.move(aiMove);
            
            // Update board position
            board.position(game.fen());
            
            // Highlight the AI's move
            highlightLastMove(aiMove.from, aiMove.to);
            
            // Check for captured pieces
            checkForCapture(result);
            
            // Add move to history
            addMoveToHistory(result);
            
            // Update game status
            updateGameStatus();
        }
        
        // Player's turn again
        isPlayerTurn = true;
        
        // Update turn indicator
        updateTurnIndicator();
    }
    
    function updateTurnIndicator() {
        if (isPlayerTurn) {
            playerTurnElement.classList.add('active');
            aiTurnElement.classList.remove('active');
        } else {
            playerTurnElement.classList.remove('active');
            aiTurnElement.classList.add('active');
        }
    }
    
    function removeHighlighting() {
        $boardEl.find('.square-55d63').removeClass('highlight-square');
        $boardEl.find('.square-55d63').removeClass('highlight-target');
        $boardEl.find('.square-55d63').removeClass('highlight-last-move');
    }
    
    function highlightSquare(square) {
        $boardEl.find(`.square-${square}`).addClass('highlight-square');
    }
    
    function highlightLastMove(source, target) {
        removeHighlighting();
        highlightSquare(source);
        highlightSquare(target);
        lastMoveSource = source;
        lastMoveTarget = target;
    }
    
    function checkForCapture(move) {
        if (move.captured) {
            const color = move.color === 'w' ? 'b' : 'w';
            capturedPieces[color].push(move.captured);
            updateCapturedPieces();
        }
    }
    
    function updateCapturedPieces() {
        whiteCapturedElement.innerHTML = '';
        blackCapturedElement.innerHTML = '';
        
        capturedPieces.b.forEach(piece => {
            const pieceIcon = getPieceIcon('b', piece);
            whiteCapturedElement.innerHTML += pieceIcon;
        });
        
        capturedPieces.w.forEach(piece => {
            const pieceIcon = getPieceIcon('w', piece);
            blackCapturedElement.innerHTML += pieceIcon;
        });
    }
    
    function getPieceIcon(color, piece) {
        const colorCode = color === 'w' ? 'white' : 'black';
        let pieceName = '';
        
        switch(piece) {
            case 'p': pieceName = 'pawn'; break;
            case 'n': pieceName = 'knight'; break;
            case 'b': pieceName = 'bishop'; break;
            case 'r': pieceName = 'rook'; break;
            case 'q': pieceName = 'queen'; break;
            case 'k': pieceName = 'king'; break;
        }
        
        return `<div class="captured-piece"><span class="piece-icon ${colorCode}-${piece}">&#${getPieceUnicode(color, piece)};</span></div>`;
    }
    
    function getPieceUnicode(color, piece) {
        const pieces = {
            'w': {
                'p': '9817',
                'n': '9816',
                'b': '9815',
                'r': '9814',
                'q': '9813',
                'k': '9812'
            },
            'b': {
                'p': '9823',
                'n': '9822',
                'b': '9821',
                'r': '9820',
                'q': '9819',
                'k': '9818'
            }
        };
        
        return pieces[color][piece];
    }
    
    function addMoveToHistory(move) {
        moveHistory.push(move);
        updateMoveList();
    }
    
    function updateMoveList() {
        moveListElement.innerHTML = '';
        
        for (let i = 0; i < moveHistory.length; i += 2) {
            const moveNumber = Math.floor(i / 2) + 1;
            const whiteMove = moveHistory[i] ? moveHistory[i].san : '';
            const blackMove = moveHistory[i + 1] ? moveHistory[i + 1].san : '';
            
            const movePair = document.createElement('div');
            movePair.className = 'move-pair';
            
            const moveNumElement = document.createElement('div');
            moveNumElement.className = 'move-number';
            moveNumElement.textContent = moveNumber + '.';
            
            const whiteMoveElement = document.createElement('div');
            whiteMoveElement.className = 'move-white';
            whiteMoveElement.textContent = whiteMove;
            
            const blackMoveElement = document.createElement('div');
            blackMoveElement.className = 'move-black';
            blackMoveElement.textContent = blackMove;
            
            movePair.appendChild(moveNumElement);
            movePair.appendChild(whiteMoveElement);
            movePair.appendChild(blackMoveElement);
            
            moveListElement.appendChild(movePair);
        }
        
        // Scroll to bottom of move list
        moveListElement.scrollTop = moveListElement.scrollHeight;
    }
    
    function updateGameStatus() {
        let statusText = '';
        
        if (game.in_checkmate()) {
            statusText = 'Game over, ' + (game.turn() === 'w' ? 'black' : 'white') + ' is victorious!';
        } else if (game.in_draw()) {
            statusText = 'Game over, drawn position';
        } else if (game.in_check()) {
            statusText = (game.turn() === 'w' ? 'White' : 'Black') + ' is in check!';
        } else {
            statusText = isPlayerTurn ? 'Your turn' : 'AI is thinking...';
        }
        
        updateStatus(statusText);
    }
    
    function updateStatus(message) {
        statusElement.textContent = message;
    }
    
    function calculateCapturedPieces(game) {
        const capturedPieces = { w: [], b: [] };
        const history = game.history({ verbose: true });
        
        history.forEach(move => {
            if (move.captured) {
                const color = move.color === 'w' ? 'b' : 'w';
                capturedPieces[color].push(move.captured);
            }
        });
        
        return capturedPieces;
    }
    
    // ===== MULTIPLAYER MODE =====
    
    // Connection setup event listeners
    createBtn.addEventListener('click', () => {
        connectionSetup.style.display = 'block';
        modeSelector.style.display = 'none';
        createGameSection.style.display = 'block';
        joinGameSection.style.display = 'none';
        statusText.textContent = 'Setting up game...';
        copyCodeBtn.disabled = true;
        initializePeer();
    });
    
    joinBtn.addEventListener('click', () => {
        connectionSetup.style.display = 'block';
        modeSelector.style.display = 'none';
        createGameSection.style.display = 'none';
        joinGameSection.style.display = 'block';
        joinStatusText.textContent = 'Setting up connection...';
        connectBtn.disabled = true;
        initializePeer();
    });
    
    connectBtn.addEventListener('click', () => {
        const code = gameCodeInput.value.trim().toUpperCase();
        if (code && code.length >= 4) {
            joinStatusText.textContent = `Connecting to game ${code}...`;
            connectBtn.disabled = true;
            connectToPeer(code);
        } else {
            joinStatusText.textContent = 'Please enter a valid game code';
        }
    });
    
    copyCodeBtn.addEventListener('click', () => {
        const gameCode = gameCodeDisplay.value.trim();
        
        if (!gameCode) {
            statusText.textContent = 'No game code available to copy';
            return;
        }
        
        // Use modern clipboard API with fallback
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(gameCode)
                .then(() => {
                    statusText.textContent = 'Game code copied to clipboard!';
                    setTimeout(() => {
                        statusText.textContent = 'Share this code with your opponent';
                    }, 2000);
                })
                .catch(err => {
                    console.error('Failed to copy code: ', err);
                    fallbackCopy();
                });
        } else {
            fallbackCopy();
        }
        
        function fallbackCopy() {
            // Select the text
            gameCodeDisplay.select();
            gameCodeDisplay.setSelectionRange(0, 99999); // For mobile devices
            
            try {
                // Execute copy command
                const successful = document.execCommand('copy');
                if (successful) {
                    statusText.textContent = 'Game code copied to clipboard!';
                    setTimeout(() => {
                        statusText.textContent = 'Share this code with your opponent';
                    }, 2000);
                } else {
                    statusText.textContent = 'Please select and copy the code manually';
                }
            } catch (err) {
                console.error('Fallback copy failed:', err);
                statusText.textContent = 'Please select and copy the code manually';
            }
        }
    });
    
    resignBtn.addEventListener('click', () => {
        if (connection && connection.open) {
            connection.send({
                type: 'resign'
            });
            mpStatusMessage.textContent = 'You resigned. Game over.';
            disableMpBoard();
        }
    });
    
    exitMpBtn.addEventListener('click', () => {
        exitMultiplayerGame();
    });
    
    // Initialize PeerJS for multiplayer
    function initializePeer() {
        try {
            // Generate a random ID if creating a game
            const isCreatingGame = createGameSection.style.display === 'block';
            
            if (isCreatingGame) {
                // Generate a random 6-character game code
                const gameCode = generateGameCode();
                console.log("Generated game code:", gameCode);
                
                // Create a new peer with the generated ID
                peer = new Peer(gameCode, {
                    debug: 2
                });
            } else {
                // Create a peer with a random ID for joining a game
                peer = new Peer({
                    debug: 2
                });
            }
            
            // Handle successful connection to the PeerJS server
            peer.on('open', (id) => {
                console.log("Connected to PeerJS server with ID:", id);
                
                if (isCreatingGame) {
                    // Display the game code for the host to share
                    gameCodeDisplay.value = id;
                    statusText.textContent = 'Game created! Share the code above with your opponent.';
                    copyCodeBtn.disabled = false;
                } else {
                    console.log("Ready to join a game");
                    joinStatusText.textContent = 'Ready to join a game';
                    connectBtn.disabled = false;
                }
            });
            
            // Listen for incoming connections if creating a game
            if (isCreatingGame) {
                peer.on('connection', (conn) => {
                    console.log("Incoming connection from:", conn.peer);
                    statusText.textContent = 'Opponent is connecting...';
                    
                    // Only accept one connection
                    if (connection && connection.open) {
                        console.log("Rejecting additional connection attempt");
                        conn.close();
                        return;
                    }
                    
                    connection = conn;
                    
                    connection.on('open', () => {
                        console.log("Connection established with:", connection.peer);
                        statusText.textContent = 'Opponent connected! Game starting...';
                        setupConnectionHandlers();
                        
                        // Switch to game view
                        setTimeout(() => {
                            createGameSection.style.display = 'none';
                            connectionSetup.style.display = 'none';
                            multiplayerGame.style.display = 'block';
                            
                            // Initialize multiplayer game as white
                            mpPlayerSide = 'w';
                            initializeMultiplayerGame();
                        }, 1000);
                    });
                });
            }
            
            // Handle connection errors
            peer.on('error', (err) => {
                console.error("PeerJS error:", err);
                
                if (err.type === 'unavailable-id') {
                    // If creating a game and the ID is taken
                    if (isCreatingGame) {
                        statusText.textContent = 'Game code already in use. Try again.';
                        // Try again with a new code
                        setTimeout(initializePeer, 1000);
                    }
                } else if (err.type === 'peer-unavailable') {
                    // If joining and the peer is not available
                    joinStatusText.textContent = 'Game not found. Check the code and try again.';
                    connectBtn.disabled = false;
                } else {
                    // Generic error handling
                    const message = isCreatingGame ? statusText : joinStatusText;
                    message.textContent = 'Connection error: ' + err.message;
                }
            });
            
            // Handle disconnection from the PeerJS server
            peer.on('disconnected', () => {
                console.log("Disconnected from PeerJS server");
                const message = isCreatingGame ? statusText : joinStatusText;
                message.textContent = 'Disconnected from server. Reconnecting...';
                
                // Try to reconnect
                peer.reconnect();
            });
            
        } catch (e) {
            console.error("Error initializing PeerJS:", e);
            const message = createGameSection.style.display === 'block' ? statusText : joinStatusText;
            message.textContent = 'Connection error: ' + e.message;
        }
    }
    
    // Generate a random game code (6 uppercase alphanumeric characters)
    function generateGameCode() {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Omitting similar looking chars
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    // Setup event handlers for the data connection
    function setupConnectionHandlers() {
        if (!connection) {
            console.error("No connection available to setup handlers");
            return;
        }
        
        // Handle incoming game data
        connection.on('data', function(data) {
            console.log("Received data:", data);
            handleGameData(data);
        });
        
        connection.on('close', function() {
            console.log("Connection closed by peer");
            mpStatusMessage.textContent = 'Opponent disconnected';
            disableMpBoard();
        });
        
        connection.on('error', function(err) {
            console.error('Connection error:', err);
            mpStatusMessage.textContent = 'Connection error: ' + err.message;
        });
    }
    
    // Handle incoming game data
    function handleGameData(data) {
        try {
            console.log('Processing game data:', data);
            
            if (!data || !data.type) {
                console.error("Invalid data received:", data);
                return;
            }
            
            switch (data.type) {
                case 'game-init':
                    console.log("Initializing game as:", data.playerSide);
                    mpPlayerSide = data.playerSide;
                    initializeMultiplayerGame();
                    break;
                    
                case 'move':
                    if (!data.from || !data.to) {
                        console.error("Invalid move data:", data);
                        return;
                    }
                    
                    console.log(`Processing opponent move: ${data.from} to ${data.to}`);
                    
                    // Make the opponent's move
                    const result = mpGame.move({
                        from: data.from,
                        to: data.to,
                        promotion: data.promotion || 'q'
                    });
                    
                    if (!result) {
                        console.error("Invalid move attempted:", data);
                        return;
                    }
                    
                    // Update board
                    mpBoard.position(mpGame.fen());
                    
                    // Highlight the move
                    highlightMpLastMove(data.from, data.to);
                    
                    // Check for captured pieces
                    checkMpForCapture(result);
                    
                    // Add move to history
                    addMpMoveToHistory(result);
                    
                    // Update status
                    updateMpGameStatus();
                    break;
                    
                case 'resign':
                    console.log("Opponent resigned");
                    mpStatusMessage.textContent = 'Opponent resigned. You win!';
                    disableMpBoard();
                    break;
                    
                default:
                    console.warn("Unknown data type received:", data.type);
            }
        } catch (e) {
            console.error("Error handling game data:", e);
            mpStatusMessage.textContent = 'Error processing game data';
        }
    }
    
    // Send game data to the peer
    function sendGameData(data) {
        if (connection && connection.open) {
            try {
                console.log("Sending data:", data);
                connection.send(data);
            } catch (e) {
                console.error("Error sending data:", e);
                mpStatusMessage.textContent = 'Error sending move';
            }
        } else {
            console.error("Cannot send data - connection not open");
            mpStatusMessage.textContent = 'Connection lost';
        }
    }
    
    // Functions for multiplayer mode
    function onMpDragStart(source, piece) {
        // Don't allow moves if game is over
        if (mpGame.game_over()) return false;
        
        // Only allow the player to move their own pieces and only on their turn
        if ((mpGame.turn() === 'w' && piece.search(/^b/) !== -1) ||
            (mpGame.turn() === 'b' && piece.search(/^w/) !== -1) ||
            (mpGame.turn() === 'w' && mpPlayerSide === 'b') ||
            (mpGame.turn() === 'b' && mpPlayerSide === 'w')) {
            return false;
        }
        
        // Highlight valid target squares if on desktop
        if (!isMobile) {
            const moves = mpGame.moves({
                square: source,
                verbose: true
            });
            
            for (let move of moves) {
                $mpBoardEl.find(`.square-${move.to}`).addClass('highlight-target');
            }
        }
        
        return true;
    }
    
    function onMpDrop(source, target, piece, newPos, oldPos, orientation) {
        // Remove highlights
        removeMpHighlighting();
        
        // See if the move is legal
        const move = mpGame.move({
            from: source,
            to: target,
            promotion: 'q' // Always promote to queen for simplicity
        });
        
        // Illegal move
        if (move === null) return 'snapback';
        
        // Check for captures
        checkMpForCapture({ move });
        
        // Highlight last move
        highlightMpLastMove(source, target);
        mpLastMoveSource = source;
        mpLastMoveTarget = target;
        
        // Add move to history and update game status
        addMpMoveToHistory(move);
        updateMpGameStatus();
        
        // Send move to opponent
        sendGameData({
            type: 'move',
            move: move,
            fen: mpGame.fen()
        });
    }
    
    function onMpSnapEnd() {
        mpBoard.position(mpGame.fen());
    }
    
    function removeMpHighlighting() {
        $mpBoardEl.find('.square-55d63').removeClass('highlight-square');
        $mpBoardEl.find('.square-55d63').removeClass('highlight-target');
        $mpBoardEl.find('.square-55d63').removeClass('highlight-last-move');
    }
    
    function highlightMpSquare(square) {
        $mpBoardEl.find(`.square-${square}`).addClass('highlight-square');
    }
    
    function highlightMpLastMove(source, target) {
        // Remove previous highlights
        $('.mp-square-highlight').removeClass('mp-square-highlight');
        
        // Add highlights to source and target squares
        $('#mp-game-board .square-' + source).addClass('mp-square-highlight');
        $('#mp-game-board .square-' + target).addClass('mp-square-highlight');
    }
    
    function checkMpForCapture(moveResult) {
        if (moveResult.captured) {
            console.log(`Piece captured: ${moveResult.captured} by ${moveResult.color}`);
            updateMpCapturedPieces();
        }
    }
    
    function updateMpCapturedPieces() {
        // Get captured pieces
        const captured = calculateMpCapturedPieces();
        
        // Update white's captured pieces
        let whiteCapturedHTML = '';
        captured.byWhite.forEach(piece => {
            whiteCapturedHTML += `<img src="${pieceTheme.replace('{piece}', 'b' + piece)}" class="captured-piece" alt="${piece}">`;
        });
        mpWhiteCaptured.innerHTML = whiteCapturedHTML;
        
        // Update black's captured pieces
        let blackCapturedHTML = '';
        captured.byBlack.forEach(piece => {
            blackCapturedHTML += `<img src="${pieceTheme.replace('{piece}', 'w' + piece)}" class="captured-piece" alt="${piece}">`;
        });
        mpBlackCaptured.innerHTML = blackCapturedHTML;
    }
    
    function calculateMpCapturedPieces() {
        const fen = mpGame.fen();
        const pieces = fen.split(' ')[0];
        
        // Count pieces on the board
        const pieceCounts = {
            'p': 0, 'n': 0, 'b': 0, 'r': 0, 'q': 0,
            'P': 0, 'N': 0, 'B': 0, 'R': 0, 'Q': 0
        };
        
        for (let i = 0; i < pieces.length; i++) {
            const piece = pieces[i];
            if (pieceCounts.hasOwnProperty(piece)) {
                pieceCounts[piece]++;
            }
        }
        
        // Calculate captured pieces
        const captured = {
            byWhite: [],
            byBlack: []
        };
        
        // Pawns (8 for each side)
        for (let i = 0; i < 8 - pieceCounts['p']; i++) captured.byWhite.push('p');
        for (let i = 0; i < 8 - pieceCounts['P']; i++) captured.byBlack.push('p');
        
        // Knights (2 for each side)
        for (let i = 0; i < 2 - pieceCounts['n']; i++) captured.byWhite.push('n');
        for (let i = 0; i < 2 - pieceCounts['N']; i++) captured.byBlack.push('n');
        
        // Bishops (2 for each side)
        for (let i = 0; i < 2 - pieceCounts['b']; i++) captured.byWhite.push('b');
        for (let i = 0; i < 2 - pieceCounts['B']; i++) captured.byBlack.push('b');
        
        // Rooks (2 for each side)
        for (let i = 0; i < 2 - pieceCounts['r']; i++) captured.byWhite.push('r');
        for (let i = 0; i < 2 - pieceCounts['R']; i++) captured.byBlack.push('r');
        
        // Queens (1 for each side)
        for (let i = 0; i < 1 - pieceCounts['q']; i++) captured.byWhite.push('q');
        for (let i = 0; i < 1 - pieceCounts['Q']; i++) captured.byBlack.push('q');
        
        return captured;
    }
    
    function addMpMoveToHistory(move) {
        // Create a new move item
        const moveItem = document.createElement('div');
        moveItem.className = 'move-item';
        
        // Format the move as SAN notation with number
        const moveNumber = Math.floor((mpGame.history().length + 1) / 2);
        const isWhiteMove = mpGame.history().length % 2 === 1;
        
        if (isWhiteMove) {
            moveItem.innerHTML = `${moveNumber}. ${move.san}`;
        } else {
            // Find the previous move item and update it
            if (mpMoveListElement.lastChild) {
                mpMoveListElement.lastChild.innerHTML += ` ${move.san}`;
                return;
            } else {
                // Fallback if there's no previous move
                moveItem.innerHTML = `${moveNumber}... ${move.san}`;
            }
        }
        
        mpMoveListElement.appendChild(moveItem);
        
        const lastMoveItem = mpMoveListElement.lastChild;
            if (lastMoveItem) {
            lastMoveItem.scrollIntoView();
        }
        
        // Auto-scroll to the bottom
        mpMoveListElement.scrollTop = mpMoveListElement.scrollHeight;
    }
    
    function clearMpMoveHistory() {
        mpMoveListElement.innerHTML = '';
    }
    
    function updateMpGameStatus() {
        let statusText = '';
        
        // Get the current turn
        const currentTurn = mpGame.turn();
        const isPlayersTurn = currentTurn === mpPlayerSide;
        
        // Check for checkmate
        if (mpGame.in_checkmate()) {
            const winner = currentTurn === 'w' ? 'Black' : 'White';
            statusText = `Checkmate! ${winner} wins!`;
            disableMpBoard();
        }
        // Check for draw
        else if (mpGame.in_draw()) {
            statusText = 'Game ended in draw!';
            disableMpBoard();
        }
        // Check for stalemate
        else if (mpGame.in_stalemate()) {
            statusText = 'Stalemate! Game ended in draw.';
            disableMpBoard();
        }
        // Check for threefold repetition
        else if (mpGame.in_threefold_repetition()) {
            statusText = 'Draw by threefold repetition!';
            disableMpBoard();
        }
        // Check for insufficient material
        else if (mpGame.insufficient_material()) {
            statusText = 'Draw by insufficient material!';
            disableMpBoard();
        }
        // Check for check
        else if (mpGame.in_check()) {
            const inCheck = currentTurn === 'w' ? 'White' : 'Black';
            if (isPlayersTurn) {
                statusText = `${inCheck} is in check! Your move.`;
            } else {
                statusText = `${inCheck} is in check! Waiting for opponent.`;
            }
        }
        // Normal game status
        else {
            if (isPlayersTurn) {
                const playerColor = mpPlayerSide === 'w' ? 'White' : 'Black';
                statusText = `Your turn (${playerColor})`;
            } else {
                const opponentColor = mpPlayerSide === 'w' ? 'Black' : 'White';
                statusText = `Waiting for opponent (${opponentColor}) to move`;
            }
        }
        
        // Update the status message
        mpStatusMessage.textContent = statusText;
    }
    
    function disableMpBoard() {
        mpBoard.draggable = false;
    }
    
    function exitMultiplayerGame() {
        // Close the connection
        if (connection) {
            connection.close();
        }
        
        // Close the peer
        if (peer) {
            peer.destroy();
            peer = null;
        }
        
        // Reset game state
        mpGame = new Chess();
        connection = null;
        isHost = false;
        mpPlayerSide = 'w';
        mpCapturedPieces = { w: [], b: [] };
        
        // Reset UI
        multiplayerGame.style.display = 'none';
        connectionSetup.style.display = 'block';
        createGameSection.style.display = 'none';
        joinGameSection.style.display = 'none';
        
        // Return to mode selector
        multiplayerMode.style.display = 'none';
        modeSelector.style.display = 'block';
    }

    // Connect to a peer as a client
    function connectToPeer(peerId) {
        try {
            joinStatusText.textContent = 'Connecting...';
            
            // Clean the peer ID (remove spaces, convert to uppercase)
            const cleanPeerId = peerId.trim().toUpperCase();
            
            if (!cleanPeerId) {
                joinStatusText.textContent = 'Please enter a valid game code';
                return;
            }
            
            console.log("Attempting to connect to peer:", cleanPeerId);
            
            // Add a timeout for connection attempts
            let connectionTimeout = setTimeout(() => {
                if (connection && !connection.open) {
                    joinStatusText.textContent = 'Connection timed out. Host may not be available.';
                    console.error("Connection attempt timed out");
                    // Clean up the failed connection
                    if (connection) {
                        connection.close();
                    }
                }
            }, 10000); // 10 second timeout
            
            connection = peer.connect(cleanPeerId, {
                reliable: true,
                serialization: 'json'
            });
            
            connection.on('open', () => {
                clearTimeout(connectionTimeout);
                console.log("Connected to peer:", connection.peer);
                joinStatusText.textContent = 'Connected! Game starting...';
                setupConnectionHandlers();
                
                // Switch to game view after successful connection
                setTimeout(() => {
                    joinGameSection.style.display = 'none';
                    connectionSetup.style.display = 'none';
                    multiplayerGame.style.display = 'block';
                    
                    // Initialize multiplayer game as black
                    mpPlayerSide = 'b';
                    initializeMultiplayerGame();
                }, 1000);
            });
            
            connection.on('error', (err) => {
                clearTimeout(connectionTimeout);
                console.error("Connection error:", err);
                joinStatusText.textContent = 'Connection error: ' + (err.message || 'Failed to connect');
            });
            
            // Handle case where the connection does not open
            connection.on('close', () => {
                clearTimeout(connectionTimeout);
                if (!multiplayerGame.style.display || multiplayerGame.style.display === 'none') {
                    joinStatusText.textContent = 'Connection closed before game could start';
                }
            });
        } catch (e) {
            console.error("Error connecting to peer:", e);
            joinStatusText.textContent = 'Connection failed: ' + e.message;
        }
    }

    // Initialize multiplayer game
    function initializeMultiplayerGame() {
        // Create a new Chess.js instance
        mpGame = new Chess();
        
        // Initialize chessboard
        const mpBoardConfig = {
            position: 'start',
            pieceTheme: pieceTheme,
            draggable: !isMobile,  // Disable dragging on mobile
            orientation: mpPlayerSide === 'w' ? 'white' : 'black',
            onDragStart: onMpDragStart,
            onDrop: onMpDrop,
            onSnapEnd: onMpSnapEnd,
            sparePieces: false,
            showNotation: false,
            snapbackSpeed: 250,
            snapSpeed: 100
        };
        
        $mpBoardEl = $('#mp-game-board');
        mpBoard = Chessboard('mp-game-board', mpBoardConfig);
        
        // Setup the board for mobile clicks
        setupMobileTapHandling();
        
        // Prevent scrolling on touch devices
        preventScrollOnChessboard();
        
        // Set the player color info
        mpPlayerColor.textContent = mpPlayerSide === 'w' ? 'White' : 'Black';
        mpPlayerColor.className = mpPlayerSide === 'w' ? 'white-text' : 'black-text';
        
        // Initialize move history
        clearMpMoveHistory();
        
        // Update captured pieces
        mpCapturedPieces = { w: [], b: [] };
        updateMpCapturedPieces();
        
        // Update status
        updateMpGameStatus();
        
        // Resize handler
        $(window).resize(() => {
            if (mpBoard) mpBoard.resize();
        });
        
        // Set up connection data handlers if not already set
        if (connection && !connection.listenerCount('data')) {
            setupConnectionHandlers();
        }
    }

    // Event handler for resign button click
    resignBtn.addEventListener('click', () => {
        if (!connection || !connection.open) {
            mpStatusMessage.textContent = 'No connection to opponent';
            return;
        }
        
        if (confirm('Are you sure you want to resign?')) {
            console.log('Player resigning');
            
            // Send resign message to opponent
            sendGameData({
                type: 'resign'
            });
            
            // Update status and disable board
            mpStatusMessage.textContent = 'You resigned. Game over.';
            disableMpBoard();
        }
    });

    // Event handler for back button from multiplayer game
    exitMpBtn.addEventListener('click', () => {
        // Confirm leaving game if it's in progress
        if (connection && connection.open && !mpGame.game_over()) {
            if (!confirm('Are you sure you want to leave the game? This will count as a resignation.')) {
                return;
            }
            
            // Notify opponent of resignation if connected
            sendGameData({
                type: 'resign'
            });
        }
        
        // Clean up connection and reset UI
        cleanupMpConnection();
        resetMpUI();
    });

    // Clean up the multiplayer connection
    function cleanupMpConnection() {
        console.log('Cleaning up multiplayer connection');
        
        if (connection) {
            connection.close();
            connection = null;
        }
        
        if (peer) {
            peer.destroy();
            peer = null;
        }
    }

    // Reset the multiplayer UI
    function resetMpUI() {
        multiplayerGame.style.display = 'none';
        connectionSetup.style.display = 'none';
        modeSelector.style.display = 'block';
        
        // Clear form fields
        gameCodeDisplay.value = '';
        gameCodeInput.value = '';
        
        // Reset status messages
        statusText.textContent = '';
        joinStatusText.textContent = '';
        mpStatusMessage.textContent = '';
        
        // Clear game elements
        resetMpCapturedPieces();
        clearMpMoveHistory();
        
        // Reset board if it exists
        if (mpBoard) {
            mpBoard.position('start');
        }
    }

    // Handle the join game button click
    connectBtn.addEventListener('click', () => {
        const gameCode = gameCodeInput.value.trim().toUpperCase();
        
        if (!gameCode) {
            joinStatusText.textContent = 'Please enter a valid game code';
            return;
        }
        
        joinStatusText.textContent = 'Connecting to game...';
        connectBtn.disabled = true;
        
        connectToPeer(gameCode);
    });

    // Add or update resize handler to ensure proper mobile display
    $(window).resize(function() {
        if (board) {
            // Get container width
            const containerWidth = $('#game-board').parent().width();
            
            // Resize board to fit container
            board.resize(containerWidth < 400 ? containerWidth : undefined);
        }
        
        if (mpBoard) {
            // Get container width
            const containerWidth = $('#mp-game-board').parent().width();
            
            // Resize board to fit container
            mpBoard.resize(containerWidth < 400 ? containerWidth : undefined);
        }
    });

    // Add global touch event handling to prevent scrolling during piece drag
    document.addEventListener('DOMContentLoaded', function() {
        console.log("Adding global touch handlers");
        // Apply touch event blocking to chess elements
        document.addEventListener('touchmove', function(e) {
            // Check if the touch is on a chess board, piece, or square
            const target = e.target;
            if (target.tagName === 'IMG' && target.classList.contains('piece-417db') ||
                (target.closest && (target.closest('#game-board') || target.closest('#mp-game-board')))) {
                console.log("Prevented scroll on chess element");
                e.preventDefault();
            }
        }, { passive: false });
    });

    // Handle click for mobile tap interface (single player)
    function handleBoardClick(square) {
        console.log('handleBoardClick called:', square, 'isMobile:', isMobile, 'isPlayerTurn:', isPlayerTurn);
        
        if (!isMobile || !isPlayerTurn) return;
        
        // Get clicked piece if any
        const position = board.position();
        const piece = position[square] || null;
        console.log('Clicked on square:', square, 'Piece:', piece);
        
        // If no piece selected yet and a valid piece is clicked
        if (!selectedSquare && piece) {
            const pieceColor = piece.charAt(0);
            
            // Check if it's the player's piece
            if ((pieceColor === 'w' && playerColor === 'w') || 
                (pieceColor === 'b' && playerColor === 'b')) {
                
                selectedSquare = square;
                selectedPiece = piece;
                console.log('Selected piece:', piece, 'at square:', square);
                
                // Highlight selected square
                removeHighlighting();
                highlightSquare(square);
                
                // Highlight valid moves
                const moves = game.moves({
                    square: square,
                    verbose: true
                });
                
                console.log('Valid moves:', moves.length);
                
                for (let move of moves) {
                    $boardEl.find(`.square-${move.to}`).addClass('highlight-target');
                }
                
                return;
            }
        }
        
        // If a piece is already selected and another square is clicked
        if (selectedSquare) {
            console.log('Attempting move from', selectedSquare, 'to', square);
            
            // Try to make the move
            const move = makeMove(selectedSquare, square);
            
            if (move) {
                console.log('Move successful');
                // Reset selection after successful move
                selectedSquare = null;
                selectedPiece = null;
                removeHighlighting();
            } else {
                console.log('Move failed or same square clicked again');
                // If illegal move or clicking the same piece again, reset selection
                selectedSquare = null;
                selectedPiece = null;
                removeHighlighting();
                
                // If clicked on another valid piece, select that instead
                setTimeout(() => handleBoardClick(square), 50);
            }
        }
    }

    // Function to make a move and handle the result
    function makeMove(source, target) {
        // Check if move is legal
        const move = game.move({
            from: source,
            to: target,
            promotion: 'q' // Auto promote to queen for simplicity
        });
        
        if (!move) return null; // Illegal move
        
        // Update board
        board.position(game.fen());
        
        // Check for captures
        checkForCapture(move);
        
        // Highlight the move
        removeHighlighting();
        highlightLastMove(source, target);
        lastMoveSource = source;
        lastMoveTarget = target;
        
        // Update move history and game status
        addMoveToHistory(move);
        updateMoveList();
        updateGameStatus();
        
        // Update turn indicator and let AI make its move
        updateTurnIndicator();
        isPlayerTurn = false;
        setTimeout(makeAIMove, 500);
        
        return move;
    }

    // New utility function to highlight valid target squares
    function highlightValidMoves(square) {
        const moves = game.moves({
            square: square,
            verbose: true
        });
        
        for (let move of moves) {
            $boardEl.find(`.square-${move.to}`).addClass('highlight-target');
        }
    }

    // Handle click for mobile tap interface (multiplayer)
    function handleMpBoardClick(square) {
        console.log('handleMpBoardClick called:', square, 'isMobile:', isMobile);
        
        if (!isMobile) return;
        
        // Only allow interaction if it's the player's turn
        const isPlayerTurn = (mpPlayerSide === 'w' && mpGame.turn() === 'w') || 
                              (mpPlayerSide === 'b' && mpGame.turn() === 'b');
        
        console.log('Is player turn:', isPlayerTurn, 'Player side:', mpPlayerSide, 'Game turn:', mpGame.turn());
        
        if (!isPlayerTurn) return;
        
        // Get clicked piece if any
        const position = mpBoard.position();
        const piece = position[square] || null;
        console.log('MP clicked on square:', square, 'Piece:', piece);
        
        // If no piece selected yet and a valid piece is clicked
        if (!selectedSquare && piece) {
            const pieceColor = piece.charAt(0);
            
            // Check if it's the player's piece
            if ((pieceColor === 'w' && mpPlayerSide === 'w') || 
                (pieceColor === 'b' && mpPlayerSide === 'b')) {
                
                selectedSquare = square;
                selectedPiece = piece;
                console.log('MP Selected piece:', piece, 'at square:', square);
                
                // Highlight selected square
                removeMpHighlighting();
                highlightMpSquare(square);
                
                // Highlight valid moves
                const moves = mpGame.moves({
                    square: square,
                    verbose: true
                });
                
                console.log('MP Valid moves:', moves.length);
                
                for (let move of moves) {
                    $mpBoardEl.find(`.square-${move.to}`).addClass('highlight-target');
                }
                
                return;
            }
        }
        
        // If a piece is already selected and another square is clicked
        if (selectedSquare) {
            console.log('MP Attempting move from', selectedSquare, 'to', square);
            
            // Try to make the move
            const moveResult = makeMpMove(selectedSquare, square);
            
            if (moveResult && moveResult.move) {
                console.log('MP Move successful');
                // Reset selection after successful move
                selectedSquare = null;
                selectedPiece = null;
                removeMpHighlighting();
                
                // Send move to opponent
                sendGameData({
                    type: 'move',
                    from: moveResult.move.from,
                    to: moveResult.move.to,
                    promotion: 'q',
                    fen: mpGame.fen()
                });
            } else {
                console.log('MP Move failed or same square clicked again');
                // If illegal move or clicking the same piece again, reset selection
                selectedSquare = null;
                selectedPiece = null;
                removeMpHighlighting();
                
                // If clicked on another valid piece, select that instead
                setTimeout(() => handleMpBoardClick(square), 50);
            }
        }
    }

    // Function to make a move and handle the result in multiplayer
    function makeMpMove(source, target) {
        // Check if move is legal
        const move = mpGame.move({
            from: source,
            to: target,
            promotion: 'q' // Auto promote to queen for simplicity
        });
        
        if (!move) return { valid: false }; // Illegal move
        
        // Update board
        mpBoard.position(mpGame.fen());
        
        // Check for captures
        checkMpForCapture({ move });
        
        // Highlight the move
        removeMpHighlighting();
        highlightMpLastMove(source, target);
        mpLastMoveSource = source;
        mpLastMoveTarget = target;
        
        // Update move history and game status
        addMpMoveToHistory(move);
        updateMpGameStatus();
        
        return { valid: true, move };
    }
});

// Add code at the end of the file to ensure the board resizes correctly
console.log('Adding chessboard layout fixes for mobile devices');

// Function to ensure proper board sizing
function fixChessboardLayout() {
    // Force the board to maintain square proportions
    const enforceSquareAspectRatio = () => {
        $('.game-board-container, .mp-game-board-container').each(function() {
            const container = $(this);
            const board = container.find('.board-b72b1');
            
            if (board.length) {
                // Make sure board maintains square aspect ratio
                board.css({
                    'display': 'flex',
                    'flex-wrap': 'wrap',
                    'width': '100%'
                });
                
                // Fix squares
                board.find('.square-55d63').css({
                    'float': 'none',
                    'display': 'flex',
                    'justify-content': 'center',
                    'align-items': 'center',
                    'width': '12.5%',
                    'padding-bottom': '12.5%',
                    'position': 'relative'
                });
                
                // Fix pieces
                board.find('.piece-417db').css({
                    'position': 'absolute',
                    'width': '80%',
                    'height': '80%',
                    'top': '10%',
                    'left': '10%',
                    'object-fit': 'contain'
                });
            }
        });
    };
    
    // Apply fixes immediately
    enforceSquareAspectRatio();
    
    // Apply fixes after board initialization and after window resize
    $(window).on('resize', enforceSquareAspectRatio);
    
    // Observe DOM changes to fix layout after board is redrawn
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length) {
                    enforceSquareAspectRatio();
                }
            });
        });
        
        // Watch for changes in the game board containers
        const containers = document.querySelectorAll('.game-board-container, .mp-game-board-container');
        containers.forEach(container => {
            observer.observe(container, { childList: true, subtree: true });
        });
    }
}

// Run the fix on page load
$(document).ready(function() {
    console.log('Document ready, applying chessboard fixes');
    
    // Apply fixes with slight delay to ensure board is fully initialized
    setTimeout(fixChessboardLayout, 1000);
    
    // Also apply when single player or multiplayer mode is shown
    $('#single-player-btn, #multiplayer-btn').on('click', function() {
        setTimeout(fixChessboardLayout, 1000);
    });
});

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
console.log('Mobile touch handlers added for better responsiveness');

console.log('Layout fixes applied');
}); c o n s o l e . l o g ( ' L a y o u t   f i x e s   a p p l i e d ' ) ; 
 
 