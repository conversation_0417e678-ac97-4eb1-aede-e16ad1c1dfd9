{"name": "p2p-chess", "version": "1.0.0", "description": "A peer-to-peer multiplayer chess game built with PeerJS", "main": "server.js", "scripts": {"start": "node server.js", "dev": "python -m http.server 8000", "serve": "npx http-server -p 8000"}, "keywords": ["chess", "p2p", "multiplayer", "webrtc", "peerjs"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2"}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/your-username/p2p-chess.git"}, "engines": {"node": ">=14.0.0"}}