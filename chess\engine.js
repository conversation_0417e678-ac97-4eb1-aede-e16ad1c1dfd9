/**
 * ChessMaestro Engine - AI player with multiple difficulty levels
 */
class ChessEngine {
    constructor(game) {
        this.game = game;
        this.maxDepth = 2; // Default depth for intermediate
        this.pieceValues = {
            'p': 100,   // pawn
            'n': 320,   // knight
            'b': 330,   // bishop
            'r': 500,   // rook
            'q': 900,   // queen
            'k': 20000  // king
        };
        
        // Position evaluation tables for improved positional play
        this.pawnEvalWhite = [
            [0,  0,  0,  0,  0,  0,  0,  0],
            [50, 50, 50, 50, 50, 50, 50, 50],
            [10, 10, 20, 30, 30, 20, 10, 10],
            [5,  5, 10, 25, 25, 10,  5,  5],
            [0,  0,  0, 20, 20,  0,  0,  0],
            [5, -5,-10,  0,  0,-10, -5,  5],
            [5, 10, 10,-20,-20, 10, 10,  5],
            [0,  0,  0,  0,  0,  0,  0,  0]
        ];
        
        this.knightEval = [
            [-50,-40,-30,-30,-30,-30,-40,-50],
            [-40,-20,  0,  0,  0,  0,-20,-40],
            [-30,  0, 10, 15, 15, 10,  0,-30],
            [-30,  5, 15, 20, 20, 15,  5,-30],
            [-30,  0, 15, 20, 20, 15,  0,-30],
            [-30,  5, 10, 15, 15, 10,  5,-30],
            [-40,-20,  0,  5,  5,  0,-20,-40],
            [-50,-40,-30,-30,-30,-30,-40,-50]
        ];
        
        this.bishopEvalWhite = [
            [-20,-10,-10,-10,-10,-10,-10,-20],
            [-10,  0,  0,  0,  0,  0,  0,-10],
            [-10,  0, 10, 10, 10, 10,  0,-10],
            [-10,  5,  5, 10, 10,  5,  5,-10],
            [-10,  0,  5, 10, 10,  5,  0,-10],
            [-10,  5,  5,  5,  5,  5,  5,-10],
            [-10,  0,  5,  0,  0,  5,  0,-10],
            [-20,-10,-10,-10,-10,-10,-10,-20]
        ];
        
        this.rookEvalWhite = [
            [0,  0,  0,  0,  0,  0,  0,  0],
            [5, 10, 10, 10, 10, 10, 10,  5],
            [-5,  0,  0,  0,  0,  0,  0, -5],
            [-5,  0,  0,  0,  0,  0,  0, -5],
            [-5,  0,  0,  0,  0,  0,  0, -5],
            [-5,  0,  0,  0,  0,  0,  0, -5],
            [-5,  0,  0,  0,  0,  0,  0, -5],
            [0,  0,  0,  5,  5,  0,  0,  0]
        ];
        
        this.queenEval = [
            [-20,-10,-10, -5, -5,-10,-10,-20],
            [-10,  0,  0,  0,  0,  0,  0,-10],
            [-10,  0,  5,  5,  5,  5,  0,-10],
            [-5,   0,  5,  5,  5,  5,  0, -5],
            [0,    0,  5,  5,  5,  5,  0, -5],
            [-10,  5,  5,  5,  5,  5,  0,-10],
            [-10,  0,  5,  0,  0,  0,  0,-10],
            [-20,-10,-10, -5, -5,-10,-10,-20]
        ];
        
        this.kingEvalWhite = [
            [-30,-40,-40,-50,-50,-40,-40,-30],
            [-30,-40,-40,-50,-50,-40,-40,-30],
            [-30,-40,-40,-50,-50,-40,-40,-30],
            [-30,-40,-40,-50,-50,-40,-40,-30],
            [-20,-30,-30,-40,-40,-30,-30,-20],
            [-10,-20,-20,-20,-20,-20,-20,-10],
            [20,  20,  0,  0,  0,  0, 20, 20],
            [20,  30, 10,  0,  0, 10, 30, 20]
        ];
    }
    
    // Set the AI's difficulty level
    setDifficulty(level) {
        switch(parseInt(level)) {
            case 1: // Beginner
                this.maxDepth = 1;
                break;
            case 2: // Intermediate
                this.maxDepth = 2;
                break;
            case 3: // Advanced
                this.maxDepth = 3;
                break;
            case 4: // Master
                this.maxDepth = 3; // Reduce from 4 to 3 for better performance
                break;
            default:
                this.maxDepth = 2;
        }
    }
    
    // Get a move from the AI
    getBestMove(game, playerColor) {
        const isMaximizingPlayer = playerColor === 'b';
        const depth = this.maxDepth;
        
        let bestMove = null;
        let bestValue = isMaximizingPlayer ? -Infinity : Infinity;
        const moves = game.moves({ verbose: true });
        
        // Handle endgame with fewer pieces more deeply
        const pieceCount = this.countPieces(game);
        const adjustedDepth = pieceCount < 10 ? Math.min(depth + 1, 3) : depth;
        
        // For very few pieces, we can afford to look deeper
        const maxCalcTime = 1500; // 1.5 seconds max calculation time
        const startTime = Date.now();
        
        // If no moves available
        if (moves.length === 0) return null;
        
        // Add some randomness to beginner level
        if (this.maxDepth === 1 && Math.random() < 0.2) {
            return moves[Math.floor(Math.random() * moves.length)];
        }
        
        // Randomize order of moves for more variety
        this.shuffleArray(moves);

        // Always have a fallback move (first legal move)
        bestMove = moves[0];
        
        // Iterative deepening for stronger play
        for (let currentDepth = 1; currentDepth <= adjustedDepth; currentDepth++) {
            // For each possible move
            for (let i = 0; i < moves.length; i++) {
                // Check if we've exceeded max calculation time
                if (Date.now() - startTime > maxCalcTime) {
                    // Return best move found so far
                    return bestMove;
                }
                
                const move = moves[i];
                
                // Make the move
                game.move(move);
                
                // Evaluate the position
                const value = this.minimax(currentDepth - 1, game, -Infinity, Infinity, !isMaximizingPlayer, startTime, maxCalcTime);
                
                // Undo the move
                game.undo();
                
                // Update best move
                if (isMaximizingPlayer) {
                    if (value > bestValue) {
                        bestValue = value;
                        bestMove = move;
                    }
                } else {
                    if (value < bestValue) {
                        bestValue = value;
                        bestMove = move;
                    }
                }
                
                // If we're out of time, return best move found
                if (Date.now() - startTime > maxCalcTime) {
                    return bestMove;
                }
            }
            
            // If we're at intermediate level or below, add some randomness
            if (currentDepth <= 2 && Math.random() < 0.1) {
                const randomIndex = Math.floor(Math.random() * moves.length);
                return moves[randomIndex];
            }
        }
        
        return bestMove;
    }
    
    // Minimax algorithm with alpha-beta pruning
    minimax(depth, game, alpha, beta, isMaximizingPlayer, startTime, maxCalcTime) {
        // Check if we've exceeded max calculation time
        if (Date.now() - startTime > maxCalcTime) {
            return this.evaluateBoard(game);
        }
        
        // If we've reached the evaluation depth or the game is over
        if (depth === 0 || game.game_over()) {
            return this.evaluateBoard(game);
        }
        
        const moves = game.moves({ verbose: true });
        
        // Randomize order of moves for more unpredictable play
        this.shuffleArray(moves);
        
        if (isMaximizingPlayer) {
            let bestValue = -Infinity;
            
            for (let i = 0; i < moves.length; i++) {
                // Check time periodically to avoid freezing
                if (i % 3 === 0 && Date.now() - startTime > maxCalcTime) {
                    return bestValue;
                }
                
                game.move(moves[i]);
                bestValue = Math.max(bestValue, this.minimax(depth - 1, game, alpha, beta, !isMaximizingPlayer, startTime, maxCalcTime));
                game.undo();
                
                alpha = Math.max(alpha, bestValue);
                if (beta <= alpha) {
                    break; // Beta cutoff
                }
            }
            
            return bestValue;
        } else {
            let bestValue = Infinity;
            
            for (let i = 0; i < moves.length; i++) {
                // Check time periodically to avoid freezing
                if (i % 3 === 0 && Date.now() - startTime > maxCalcTime) {
                    return bestValue;
                }
                
                game.move(moves[i]);
                bestValue = Math.min(bestValue, this.minimax(depth - 1, game, alpha, beta, !isMaximizingPlayer, startTime, maxCalcTime));
                game.undo();
                
                beta = Math.min(beta, bestValue);
                if (beta <= alpha) {
                    break; // Alpha cutoff
                }
            }
            
            return bestValue;
        }
    }
    
    // Evaluate the board position
    evaluateBoard(game) {
        let totalEvaluation = 0;
        
        // Material evaluation
        for (let i = 0; i < 8; i++) {
            for (let j = 0; j < 8; j++) {
                totalEvaluation += this.getPieceValue(game.board()[i][j], i, j);
            }
        }
        
        // Checkmate and check evaluation
        if (game.in_checkmate()) {
            // If black is in checkmate, white wins (high positive score)
            // If white is in checkmate, black wins (high negative score)
            return game.turn() === 'w' ? -20000 : 20000;
        }
        
        // Check penalty/bonus
        if (game.in_check()) {
            totalEvaluation += game.turn() === 'w' ? -50 : 50;
        }
        
        // Castling bonus (more aggressive for intermediate and above)
        if (this.maxDepth >= 2) {
            // Reduce castling history value for white
            if (!game.history().some(move => move.includes('O-O') && move[0] === 'K')) {
                totalEvaluation -= 30; // Penalize for not castling
            }
            
            // Reduce castling history value for black
            if (!game.history().some(move => move.includes('O-O') && move[0] === 'k')) {
                totalEvaluation += 30; // Penalize for not castling
            }
        }
        
        return totalEvaluation;
    }
    
    // Count the total number of pieces on the board
    countPieces(game) {
        let count = 0;
        for (let i = 0; i < 8; i++) {
            for (let j = 0; j < 8; j++) {
                if (game.board()[i][j]) {
                    count++;
                }
            }
        }
        return count;
    }
    
    // Get the value of a piece based on its position
    getPieceValue(piece, row, col) {
        if (piece === null) {
            return 0;
        }
        
        // Get basic material value
        let absoluteValue = this.pieceValues[piece.type];
        
        // Add positional value
        switch(piece.type) {
            case 'p': // Pawn
                absoluteValue += piece.color === 'w' ? 
                    this.pawnEvalWhite[row][col] : 
                    this.pawnEvalWhite[7 - row][col];
                break;
            case 'n': // Knight
                absoluteValue += this.knightEval[row][col];
                break;
            case 'b': // Bishop
                absoluteValue += piece.color === 'w' ? 
                    this.bishopEvalWhite[row][col] : 
                    this.bishopEvalWhite[7 - row][col];
                break;
            case 'r': // Rook
                absoluteValue += piece.color === 'w' ? 
                    this.rookEvalWhite[row][col] : 
                    this.rookEvalWhite[7 - row][col];
                break;
            case 'q': // Queen
                absoluteValue += this.queenEval[row][col];
                break;
            case 'k': // King
                absoluteValue += piece.color === 'w' ? 
                    this.kingEvalWhite[row][col] : 
                    this.kingEvalWhite[7 - row][col];
                break;
        }
        
        // Return positive value for white, negative for black
        return piece.color === 'w' ? absoluteValue : -absoluteValue;
    }
    
    // Shuffle an array in place
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }
} 