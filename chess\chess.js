��/ * * 
   *   T w o - G a m e s   C h e s s   -   S i n g l e   P l a y e r   a n d   M u l t i p l a y e r   m o d e s 
   * / 
 d o c u m e n t . a d d E v e n t L i s t e n e r ( ' D O M C o n t e n t L o a d e d ' ,   ( )   = >   { 
         / /   D O M   E l e m e n t s   -   M o d e   S e l e c t i o n 
         c o n s t   m o d e S e l e c t o r   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m o d e - s e l e c t o r ' ) ; 
         c o n s t   s i n g l e P l a y e r B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' s i n g l e - p l a y e r - b t n ' ) ; 
         c o n s t   m u l t i p l a y e r B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m u l t i p l a y e r - b t n ' ) ; 
         c o n s t   s i n g l e P l a y e r M o d e   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' s i n g l e - p l a y e r - m o d e ' ) ; 
         c o n s t   m u l t i p l a y e r M o d e   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m u l t i p l a y e r M o d e ' ) ; 
         
         / /   M o b i l e   d e t e c t i o n   -   U s e   r e a l   d e t e c t i o n   i n s t e a d   o f   f o r c i n g   m o b i l e   m o d e 
         c o n s t   i s M o b i l e   =   / A n d r o i d | w e b O S | i P h o n e | i P a d | i P o d | B l a c k B e r r y | I E M o b i l e | O p e r a   M i n i / i . t e s t ( n a v i g a t o r . u s e r A g e n t ) ; 
         / / c o n s t   i s M o b i l e   =   t r u e ;   / /   F o r c e   m o b i l e   m o d e   f o r   t e s t i n g 
         c o n s o l e . l o g ( " M o b i l e   d e v i c e   d e t e c t e d : " ,   i s M o b i l e ) ; 
         
         / /   S e l e c t e d   p i e c e   t r a c k i n g   f o r   m o b i l e   t a p   i n t e r f a c e 
         l e t   s e l e c t e d S q u a r e   =   n u l l ; 
         l e t   s e l e c t e d P i e c e   =   n u l l ; 
         
         / /   P r e v e n t   s c r o l l i n g   w h e n   d r a g g i n g   o n   c h e s s b o a r d 
         c o n s t   p r e v e n t S c r o l l O n C h e s s b o a r d   =   ( )   = >   { 
                 c o n s t   g a m e B o a r d   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' g a m e - b o a r d ' ) ; 
                 c o n s t   m p G a m e B o a r d   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m p - g a m e - b o a r d ' ) ; 
                 
                 i f   ( g a m e B o a r d )   { 
                         g a m e B o a r d . a d d E v e n t L i s t e n e r ( ' t o u c h m o v e ' ,   ( e )   = >   { 
                                 e . p r e v e n t D e f a u l t ( ) ; 
                         } ,   {   p a s s i v e :   f a l s e   } ) ; 
                         
                         / /   A d d   t o u c h   h a n d l i n g   f o r   m o b i l e   d e v i c e s 
                         i f   ( i s M o b i l e )   { 
                                 c o n s o l e . l o g ( " A d d i n g   m o b i l e   t o u c h   h a n d l e r s   t o   g a m e   b o a r d " ) ; 
                                 g a m e B o a r d . a d d E v e n t L i s t e n e r ( ' t o u c h s t a r t ' ,   o n B o a r d T o u c h S t a r t ,   f a l s e ) ; 
                                 g a m e B o a r d . a d d E v e n t L i s t e n e r ( ' t o u c h e n d ' ,   o n B o a r d T o u c h E n d ,   f a l s e ) ; 
                         } 
                 } 
                 
                 i f   ( m p G a m e B o a r d )   { 
                         m p G a m e B o a r d . a d d E v e n t L i s t e n e r ( ' t o u c h m o v e ' ,   ( e )   = >   { 
                                 e . p r e v e n t D e f a u l t ( ) ; 
                         } ,   {   p a s s i v e :   f a l s e   } ) ; 
                         
                         / /   A d d   t o u c h   h a n d l i n g   f o r   m o b i l e   d e v i c e s 
                         i f   ( i s M o b i l e )   { 
                                 c o n s o l e . l o g ( " A d d i n g   m o b i l e   t o u c h   h a n d l e r s   t o   m u l t i p l a y e r   b o a r d " ) ; 
                                 m p G a m e B o a r d . a d d E v e n t L i s t e n e r ( ' t o u c h s t a r t ' ,   o n M p B o a r d T o u c h S t a r t ,   f a l s e ) ; 
                                 m p G a m e B o a r d . a d d E v e n t L i s t e n e r ( ' t o u c h e n d ' ,   o n M p B o a r d T o u c h E n d ,   f a l s e ) ; 
                         } 
                 } 
         } ; 
         
         / /   T o u c h   e v e n t   h a n d l e r s   f o r   s i n g l e   p l a y e r 
         f u n c t i o n   o n B o a r d T o u c h S t a r t ( e )   { 
                 e . p r e v e n t D e f a u l t ( ) ; 
                 c o n s o l e . l o g ( " T o u c h   s t a r t   o n   g a m e   b o a r d " ) ; 
         } 
 
         f u n c t i o n   o n B o a r d T o u c h E n d ( e )   { 
                 e . p r e v e n t D e f a u l t ( ) ; 
                 c o n s o l e . l o g ( " T o u c h   e n d   o n   g a m e   b o a r d " ) ; 
                 
                 / /   G e t   t h e   t o u c h e d   e l e m e n t 
                 c o n s t   t o u c h   =   e . c h a n g e d T o u c h e s [ 0 ] ; 
                 c o n s t   e l e m e n t   =   d o c u m e n t . e l e m e n t F r o m P o i n t ( t o u c h . c l i e n t X ,   t o u c h . c l i e n t Y ) ; 
                 
                 / /   G e t   t h e   s q u a r e   f r o m   t h e   e l e m e n t 
                 l e t   s q u a r e   =   n u l l ; 
                 
                 i f   ( e l e m e n t . c l a s s L i s t . c o n t a i n s ( ' s q u a r e - 5 5 d 6 3 ' ) )   { 
                         s q u a r e   =   e l e m e n t . g e t A t t r i b u t e ( ' d a t a - s q u a r e ' ) ; 
                 }   e l s e   { 
                         / /   T r y   t o   f i n d   t h e   s q u a r e   p a r e n t 
                         c o n s t   s q u a r e E l e m e n t   =   e l e m e n t . c l o s e s t ( ' . s q u a r e - 5 5 d 6 3 ' ) ; 
                         i f   ( s q u a r e E l e m e n t )   { 
                                 s q u a r e   =   s q u a r e E l e m e n t . g e t A t t r i b u t e ( ' d a t a - s q u a r e ' ) ; 
                         } 
                 } 
                 
                 i f   ( s q u a r e )   { 
                         c o n s o l e . l o g ( " T o u c h   d e t e c t e d   o n   s q u a r e : " ,   s q u a r e ) ; 
                         i f   ( i s P l a y e r T u r n )   { 
                                 h a n d l e B o a r d C l i c k ( s q u a r e ) ; 
                         } 
                 } 
         } 
 
         / /   T o u c h   e v e n t   h a n d l e r s   f o r   m u l t i p l a y e r 
         f u n c t i o n   o n M p B o a r d T o u c h S t a r t ( e )   { 
                 e . p r e v e n t D e f a u l t ( ) ; 
                 c o n s o l e . l o g ( " T o u c h   s t a r t   o n   m u l t i p l a y e r   b o a r d " ) ; 
         } 
 
         f u n c t i o n   o n M p B o a r d T o u c h E n d ( e )   { 
                 e . p r e v e n t D e f a u l t ( ) ; 
                 c o n s o l e . l o g ( " T o u c h   e n d   o n   m u l t i p l a y e r   b o a r d " ) ; 
                 
                 / /   G e t   t h e   t o u c h e d   e l e m e n t 
                 c o n s t   t o u c h   =   e . c h a n g e d T o u c h e s [ 0 ] ; 
                 c o n s t   e l e m e n t   =   d o c u m e n t . e l e m e n t F r o m P o i n t ( t o u c h . c l i e n t X ,   t o u c h . c l i e n t Y ) ; 
                 
                 / /   G e t   t h e   s q u a r e   f r o m   t h e   e l e m e n t 
                 l e t   s q u a r e   =   n u l l ; 
                 
                 i f   ( e l e m e n t . c l a s s L i s t . c o n t a i n s ( ' s q u a r e - 5 5 d 6 3 ' ) )   { 
                         s q u a r e   =   e l e m e n t . g e t A t t r i b u t e ( ' d a t a - s q u a r e ' ) ; 
                 }   e l s e   { 
                         / /   T r y   t o   f i n d   t h e   s q u a r e   p a r e n t 
                         c o n s t   s q u a r e E l e m e n t   =   e l e m e n t . c l o s e s t ( ' . s q u a r e - 5 5 d 6 3 ' ) ; 
                         i f   ( s q u a r e E l e m e n t )   { 
                                 s q u a r e   =   s q u a r e E l e m e n t . g e t A t t r i b u t e ( ' d a t a - s q u a r e ' ) ; 
                         } 
                 } 
                 
                 i f   ( s q u a r e )   { 
                         c o n s o l e . l o g ( " M P   t o u c h   d e t e c t e d   o n   s q u a r e : " ,   s q u a r e ) ; 
                         h a n d l e M p B o a r d C l i c k ( s q u a r e ) ; 
                 } 
         } 
         
         / /   H a n d l e   s t a r t   p l a y i n g   b u t t o n   f r o m   h o m e p a g e   i f   d i r e c t e d   h e r e 
         c o n s t   u r l P a r a m s   =   n e w   U R L S e a r c h P a r a m s ( w i n d o w . l o c a t i o n . s e a r c h ) ; 
         i f   ( u r l P a r a m s . h a s ( ' s t a r t ' ) )   { 
                 / /   I f   d i r e c t e d   w i t h   ? s t a r t = s i n g l e   o r   ? s t a r t = m u l t i ,   a u t o - s t a r t   t h a t   m o d e 
                 c o n s t   s t a r t M o d e   =   u r l P a r a m s . g e t ( ' s t a r t ' ) ; 
                 i f   ( s t a r t M o d e   = = =   ' s i n g l e ' )   { 
                         s e t T i m e o u t ( ( )   = >   s i n g l e P l a y e r B t n . c l i c k ( ) ,   5 0 0 ) ; 
                 }   e l s e   i f   ( s t a r t M o d e   = = =   ' m u l t i ' )   { 
                         s e t T i m e o u t ( ( )   = >   m u l t i p l a y e r B t n . c l i c k ( ) ,   5 0 0 ) ; 
                 } 
         } 
         
         / /   S i n g l e   P l a y e r   M o d e   E l e m e n t s 
         c o n s t   d i f f i c u l t y S e l e c t o r   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' d i f f i c u l t y ' ) ; 
         c o n s t   p l a y W h i t e B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' p l a y - w h i t e ' ) ; 
         c o n s t   p l a y B l a c k B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' p l a y - b l a c k ' ) ; 
         c o n s t   n e w G a m e B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' n e w - g a m e - b t n ' ) ; 
         c o n s t   u n d o B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' u n d o - b t n ' ) ; 
         c o n s t   e x i t S i n g l e B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' e x i t - s i n g l e - b t n ' ) ; 
         c o n s t   s t a t u s E l e m e n t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' s t a t u s - m e s s a g e ' ) ; 
         c o n s t   p l a y e r T u r n E l e m e n t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' p l a y e r - t u r n ' ) ; 
         c o n s t   a i T u r n E l e m e n t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' a i - t u r n ' ) ; 
         c o n s t   m o v e L i s t E l e m e n t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m o v e - l i s t ' ) ; 
         c o n s t   w h i t e C a p t u r e d E l e m e n t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' w h i t e - c a p t u r e d - p i e c e s ' ) ; 
         c o n s t   b l a c k C a p t u r e d E l e m e n t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' b l a c k - c a p t u r e d - p i e c e s ' ) ; 
         
         / /   M u l t i p l a y e r   M o d e   E l e m e n t s 
         c o n s t   c o n n e c t i o n S e t u p   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' c o n n e c t i o n - s e t u p ' ) ; 
         c o n s t   c r e a t e B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' c r e a t e B t n ' ) ; 
         c o n s t   j o i n B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' j o i n B t n ' ) ; 
         c o n s t   c r e a t e G a m e S e c t i o n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' c r e a t e G a m e S e c t i o n ' ) ; 
         c o n s t   j o i n G a m e S e c t i o n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' j o i n G a m e S e c t i o n ' ) ; 
         c o n s t   g a m e C o d e D i s p l a y   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' g a m e C o d e D i s p l a y ' ) ; 
         c o n s t   g a m e C o d e I n p u t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' g a m e C o d e I n p u t ' ) ; 
         c o n s t   c o p y C o d e B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' c o p y C o d e B t n ' ) ; 
         c o n s t   c o n n e c t B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' c o n n e c t B t n ' ) ; 
         c o n s t   s t a t u s T e x t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' s t a t u s T e x t ' ) ; 
         c o n s t   j o i n S t a t u s T e x t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' j o i n S t a t u s T e x t ' ) ; 
         c o n s t   m u l t i p l a y e r G a m e   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m u l t i p l a y e r - g a m e ' ) ; 
         c o n s t   m p S t a t u s M e s s a g e   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m p - s t a t u s ' ) ; 
         c o n s t   m p P l a y e r I n f o   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m p - p l a y e r - i n f o ' ) ; 
         c o n s t   m p P l a y e r C o l o r   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m p - p l a y e r - c o l o r ' ) ; 
         c o n s t   r e s i g n B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' r e s i g n - b t n ' ) ; 
         c o n s t   e x i t M p B t n   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' e x i t - m p - b t n ' ) ; 
         c o n s t   m p M o v e L i s t E l e m e n t   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m p - m o v e - l i s t ' ) ; 
         c o n s t   m p W h i t e C a p t u r e d   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m p - w h i t e - c a p t u r e d ' ) ; 
         c o n s t   m p B l a c k C a p t u r e d   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' m p - b l a c k - c a p t u r e d ' ) ; 
         
         / /   G l o b a l   D O M   e l e m e n t s   -   M u l t i p l a y e r 
         c o n s t   m p B o a r d C o n t a i n e r   =   d o c u m e n t . q u e r y S e l e c t o r ( ' # m p - g a m e - b o a r d ' )   | |   d o c u m e n t . c r e a t e E l e m e n t ( ' d i v ' ) ; 
         / /   U s e   e x i s t i n g   s t a t u s T e x t   i n s t e a d   o f   r e d e f i n i n g   c r e a t e S t a t u s T e x t 
         / /   U s e   e x i s t i n g   c o n n e c t B t n   i n s t e a d   o f   r e d e f i n i n g   j o i n G a m e B t n 
         / /   U s e   e x i s t i n g   e x i t M p B t n   i n s t e a d   o f   r e d e f i n i n g   b a c k F r o m M p B t n 
         / /   U s e   e x i s t i n g   r e s i g n B t n   i n s t e a d   o f   r e d e f i n i n g   m p R e s i g n B t n 
         
         / /   G a m e   s t a t e   v a r i a b l e s   -   S i n g l e   P l a y e r 
         l e t   g a m e   =   n e w   C h e s s ( ) ; 
         l e t   b o a r d   =   n u l l ; 
         l e t   e n g i n e   =   n u l l ; 
         l e t   p l a y e r C o l o r   =   ' w ' ; 
         l e t   i s P l a y e r T u r n   =   t r u e ; 
         l e t   c a p t u r e d P i e c e s   =   {   w :   [ ] ,   b :   [ ]   } ; 
         l e t   m o v e H i s t o r y   =   [ ] ; 
         l e t   $ b o a r d E l   =   n u l l ; 
         l e t   l a s t M o v e S o u r c e   =   n u l l ; 
         l e t   l a s t M o v e T a r g e t   =   n u l l ; 
         
         / /   G a m e   s t a t e   v a r i a b l e s   -   M u l t i p l a y e r 
         l e t   m p G a m e   =   n u l l ; 
         l e t   m p B o a r d   =   n u l l ; 
         l e t   p e e r   =   n u l l ; 
         l e t   c o n n e c t i o n   =   n u l l ; 
         l e t   i s H o s t   =   f a l s e ; 
         l e t   m p P l a y e r S i d e   =   ' w ' ;   / /   ' w '   f o r   w h i t e ,   ' b '   f o r   b l a c k 
         l e t   m p C a p t u r e d P i e c e s   =   {   w :   [ ] ,   b :   [ ]   } ; 
         l e t   $ m p B o a r d E l   =   n u l l ; 
         l e t   m p L a s t M o v e S o u r c e   =   n u l l ; 
         l e t   m p L a s t M o v e T a r g e t   =   n u l l ; 
         
         / /   P i e c e   t h e m e   c o n f i g 
         c o n s t   p i e c e T h e m e   =   ( p i e c e )   = >   { 
                 r e t u r n   ` h t t p s : / / c h e s s b o a r d j s . c o m / i m g / c h e s s p i e c e s / a l p h a / $ { p i e c e } . p n g ` ; 
         } ; 
         
         / /   M o d e   S e l e c t i o n   E v e n t   L i s t e n e r s 
         s i n g l e P l a y e r B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 m o d e S e l e c t o r . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 s i n g l e P l a y e r M o d e . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 i n i t i a l i z e S i n g l e P l a y e r M o d e ( ) ; 
         } ) ; 
         
         m u l t i p l a y e r B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 m o d e S e l e c t o r . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 m u l t i p l a y e r M o d e . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 / /   I n i t i a l i z e   c r e a t e   g a m e   &   j o i n   g a m e   b u t t o n s 
                 c r e a t e B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                         c o n n e c t i o n S e t u p . q u e r y S e l e c t o r ( ' . s e t u p - b u t t o n s ' ) . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                         c r e a t e G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                         j o i n G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                         s t a t u s T e x t . t e x t C o n t e n t   =   ' S e t t i n g   u p   g a m e . . . ' ; 
                         c o p y C o d e B t n . d i s a b l e d   =   t r u e ; 
                         i n i t i a l i z e P e e r ( ) ; 
                 } ) ; 
                 
                 j o i n B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                         c o n n e c t i o n S e t u p . q u e r y S e l e c t o r ( ' . s e t u p - b u t t o n s ' ) . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                         j o i n G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                         c r e a t e G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 } ) ; 
                 
                 c o n n e c t B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                         c o n s t   c o d e   =   g a m e C o d e I n p u t . v a l u e . t r i m ( ) . t o U p p e r C a s e ( ) ; 
                         i f   ( c o d e   & &   c o d e . l e n g t h   > =   4 )   { 
                                 j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ` C o n n e c t i n g   t o   g a m e   $ { c o d e } . . . ` ; 
                                 c o n n e c t B t n . d i s a b l e d   =   t r u e ; 
                                 c o n n e c t T o P e e r ( c o d e ) ; 
                         }   e l s e   { 
                                 j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' P l e a s e   e n t e r   a   v a l i d   g a m e   c o d e ' ; 
                         } 
                 } ) ; 
         } ) ; 
         
         / /   = = = = =   S I N G L E   P L A Y E R   M O D E   = = = = = 
         
         / /   I n i t i a l i z e   s i n g l e   p l a y e r   m o d e 
         f u n c t i o n   i n i t i a l i z e S i n g l e P l a y e r M o d e ( )   { 
                 / /   I n i t i a l i z e   t h e   b o a r d   w i t h o u t   c l i c k   h a n d l e r   f o r   m o b i l e 
                 c o n s t   b o a r d C o n f i g   =   { 
                         p o s i t i o n :   ' s t a r t ' , 
                         p i e c e T h e m e :   p i e c e T h e m e , 
                         d r a g g a b l e :   ! i s M o b i l e ,     / /   D i s a b l e   d r a g g i n g   o n   m o b i l e 
                         o n D r a g S t a r t :   o n D r a g S t a r t , 
                         o n D r o p :   o n D r o p , 
                         o n S n a p E n d :   o n S n a p E n d , 
                         s p a r e P i e c e s :   f a l s e , 
                         s h o w N o t a t i o n :   f a l s e , 
                         s n a p b a c k S p e e d :   2 5 0 , 
                         s n a p S p e e d :   1 0 0 , 
                         w i d t h :   c a l c u l a t e B o a r d S i z e ( )   / /   U s e   c a l c u l a t e d   r e s p o n s i v e   s i z e 
                 } ; 
                 
                 / /   I f   b o a r d   a l r e a d y   e x i s t s ,   d e s t r o y   i t   f i r s t 
                 i f   ( b o a r d )   { 
                         b o a r d . d e s t r o y ( ) ; 
                 } 
                 
                 $ b o a r d E l   =   $ ( ' # g a m e - b o a r d ' ) ; 
                 b o a r d   =   C h e s s b o a r d ( ' g a m e - b o a r d ' ,   b o a r d C o n f i g ) ; 
                 
                 / /   I n i t i a l i z e   t h e   c h e s s   e n g i n e 
                 e n g i n e   =   n e w   C h e s s E n g i n e ( g a m e ) ; 
                 e n g i n e . s e t D i f f i c u l t y ( d i f f i c u l t y S e l e c t o r . v a l u e ) ; 
                 
                 / /   S e t u p   m o b i l e   c l i c k   h a n d l i n g   a f t e r   b o a r d   i s   i n i t i a l i z e d 
                 s e t u p M o b i l e T a p H a n d l i n g ( ) ; 
                 
                 / /   P r e v e n t   s c r o l l i n g   o n   t o u c h   d e v i c e s 
                 p r e v e n t S c r o l l O n C h e s s b o a r d ( ) ; 
                 
                 / /   R e s i z e   h a n d l e r 
                 $ ( w i n d o w ) . r e s i z e ( ( )   = >   { 
                         i f   ( b o a r d )   { 
                                 b o a r d . r e s i z e ( c a l c u l a t e B o a r d S i z e ( ) ) ; 
                         } 
                 } ) ; 
                 
                 / /   F o r c e   a   r e s i z e   a f t e r   c r e a t i o n   t o   e n s u r e   p r o p e r   d i s p l a y 
                 s e t T i m e o u t ( ( )   = >   { 
                         i f   ( b o a r d )   { 
                                 b o a r d . r e s i z e ( c a l c u l a t e B o a r d S i z e ( ) ) ; 
                         } 
                 } ,   1 0 0 ) ; 
                 
                 / /   S t a r t   a   n e w   g a m e 
                 s t a r t N e w G a m e ( ) ; 
         } 
         
         / /   S e t u p   m o b i l e   t a p   h a n d l i n g   f o r   b o t h   b o a r d s 
         f u n c t i o n   s e t u p M o b i l e T a p H a n d l i n g ( )   { 
                 i f   ( i s M o b i l e )   { 
                         / /   C l e a r   a n y   p r e v i o u s   h a n d l e r s 
                         $ ( ' # g a m e - b o a r d ' ) . o f f ( ' c l i c k ' ,   ' . s q u a r e - 5 5 d 6 3 ' ) ; 
                         
                         / /   A d d   c l i c k   e v e n t   d i r e c t l y   t o   a l l   s q u a r e s 
                         $ ( ' # g a m e - b o a r d ' ) . o n ( ' c l i c k ' ,   ' . s q u a r e - 5 5 d 6 3 ' ,   f u n c t i o n ( )   { 
                                 c o n s t   s q u a r e   =   $ ( t h i s ) . a t t r ( ' d a t a - s q u a r e ' ) ; 
                                 c o n s o l e . l o g ( " S q u a r e   c l i c k e d : " ,   s q u a r e ) ; 
                                 i f   ( i s P l a y e r T u r n )   { 
                                         h a n d l e B o a r d C l i c k ( s q u a r e ) ; 
                                 } 
                         } ) ; 
                         
                         / /   D o   t h e   s a m e   f o r   m u l t i p l a y e r   b o a r d 
                         $ ( ' # m p - g a m e - b o a r d ' ) . o f f ( ' c l i c k ' ,   ' . s q u a r e - 5 5 d 6 3 ' ) ; 
                         $ ( ' # m p - g a m e - b o a r d ' ) . o n ( ' c l i c k ' ,   ' . s q u a r e - 5 5 d 6 3 ' ,   f u n c t i o n ( )   { 
                                 c o n s t   s q u a r e   =   $ ( t h i s ) . a t t r ( ' d a t a - s q u a r e ' ) ; 
                                 c o n s o l e . l o g ( " M P   S q u a r e   c l i c k e d : " ,   s q u a r e ) ; 
                                 h a n d l e M p B o a r d C l i c k ( s q u a r e ) ; 
                         } ) ; 
                 } 
         } 
         
         / /   E v e n t   l i s t e n e r s   f o r   s i n g l e   p l a y e r   m o d e 
         d i f f i c u l t y S e l e c t o r . a d d E v e n t L i s t e n e r ( ' c h a n g e ' ,   ( )   = >   { 
                 e n g i n e . s e t D i f f i c u l t y ( d i f f i c u l t y S e l e c t o r . v a l u e ) ; 
                 u p d a t e S t a t u s ( ` D i f f i c u l t y   s e t   t o   $ { d i f f i c u l t y S e l e c t o r . o p t i o n s [ d i f f i c u l t y S e l e c t o r . s e l e c t e d I n d e x ] . t e x t } ` ) ; 
         } ) ; 
         
         p l a y W h i t e B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 p l a y W h i t e B t n . c l a s s L i s t . a d d ( ' a c t i v e ' ) ; 
                 p l a y B l a c k B t n . c l a s s L i s t . r e m o v e ( ' a c t i v e ' ) ; 
                 i f   ( p l a y e r C o l o r   ! = =   ' w ' )   { 
                         p l a y e r C o l o r   =   ' w ' ; 
                         s t a r t N e w G a m e ( ) ; 
                 } 
         } ) ; 
         
         p l a y B l a c k B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 p l a y B l a c k B t n . c l a s s L i s t . a d d ( ' a c t i v e ' ) ; 
                 p l a y W h i t e B t n . c l a s s L i s t . r e m o v e ( ' a c t i v e ' ) ; 
                 i f   ( p l a y e r C o l o r   ! = =   ' b ' )   { 
                         p l a y e r C o l o r   =   ' b ' ; 
                         s t a r t N e w G a m e ( ) ; 
                 } 
         } ) ; 
         
         n e w G a m e B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   s t a r t N e w G a m e ) ; 
         
         u n d o B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 i f   ( g a m e . h i s t o r y ( ) . l e n g t h   > =   2 )   { 
                         g a m e . u n d o ( ) ;   / /   U n d o   A I ' s   m o v e 
                         g a m e . u n d o ( ) ;   / /   U n d o   p l a y e r ' s   m o v e 
                         b o a r d . p o s i t i o n ( g a m e . f e n ( ) ) ; 
                         c a p t u r e d P i e c e s   =   c a l c u l a t e C a p t u r e d P i e c e s ( g a m e ) ; 
                         u p d a t e C a p t u r e d P i e c e s ( ) ; 
                         u p d a t e M o v e L i s t ( ) ; 
                         i s P l a y e r T u r n   =   t r u e ; 
                         u p d a t e T u r n I n d i c a t o r ( ) ; 
                         u p d a t e S t a t u s ( " M o v e   u n d o n e .   Y o u r   t u r n . " ) ; 
                 } 
         } ) ; 
         
         e x i t S i n g l e B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 s i n g l e P l a y e r M o d e . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 m o d e S e l e c t o r . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 r e s e t S i n g l e P l a y e r G a m e ( ) ; 
         } ) ; 
         
         / /   F u n c t i o n s   f o r   s i n g l e   p l a y e r   m o d e 
         f u n c t i o n   s t a r t N e w G a m e ( )   { 
                 g a m e   =   n e w   C h e s s ( ) ; 
                 b o a r d . p o s i t i o n ( g a m e . f e n ( ) ) ; 
                 
                 c a p t u r e d P i e c e s   =   {   w :   [ ] ,   b :   [ ]   } ; 
                 u p d a t e C a p t u r e d P i e c e s ( ) ; 
                 
                 m o v e H i s t o r y   =   [ ] ; 
                 u p d a t e M o v e L i s t ( ) ; 
                 
                 i s P l a y e r T u r n   =   p l a y e r C o l o r   = = =   ' w ' ; 
                 u p d a t e T u r n I n d i c a t o r ( ) ; 
                 
                 u p d a t e S t a t u s ( i s P l a y e r T u r n   ?   " Y o u r   t u r n "   :   " A I   i s   t h i n k i n g . . . " ) ; 
                 
                 / /   P r e v e n t   s c r o l l i n g   o n   t o u c h   d e v i c e s 
                 p r e v e n t S c r o l l O n C h e s s b o a r d ( ) ; 
                 
                 i f   ( ! i s P l a y e r T u r n )   { 
                         / /   I f   p l a y e r   i s   b l a c k ,   A I   ( w h i t e )   m o v e s   f i r s t 
                         s e t T i m e o u t ( m a k e A I M o v e ,   5 0 0 ) ; 
                 } 
         } 
         
         f u n c t i o n   r e s e t S i n g l e P l a y e r G a m e ( )   { 
                 g a m e   =   n e w   C h e s s ( ) ; 
                 i f   ( b o a r d )   { 
                         b o a r d . p o s i t i o n ( g a m e . f e n ( ) ) ; 
                 } 
                 c a p t u r e d P i e c e s   =   {   w :   [ ] ,   b :   [ ]   } ; 
                 m o v e H i s t o r y   =   [ ] ; 
                 i s P l a y e r T u r n   =   t r u e ; 
         } 
         
         f u n c t i o n   o n D r a g S t a r t ( s o u r c e ,   p i e c e )   { 
                 / /   D o n ' t   a l l o w   d r a g   i f   n o t   p l a y e r ' s   t u r n 
                 i f   ( ! i s P l a y e r T u r n )   r e t u r n   f a l s e ; 
                 
                 / /   O n l y   a l l o w   p l a y e r   t o   m o v e   t h e i r   o w n   p i e c e s 
                 i f   ( ( p l a y e r C o l o r   = = =   ' w '   & &   p i e c e . s e a r c h ( / ^ b / )   ! = =   - 1 )   | | 
                         ( p l a y e r C o l o r   = = =   ' b '   & &   p i e c e . s e a r c h ( / ^ w / )   ! = =   - 1 ) )   { 
                         r e t u r n   f a l s e ; 
                 } 
                 
                 / /   H i g h l i g h t   v a l i d   t a r g e t   s q u a r e s   i f   o n   d e s k t o p 
                 i f   ( ! i s M o b i l e )   { 
                         h i g h l i g h t V a l i d M o v e s ( s o u r c e ) ; 
                 } 
                 
                 r e t u r n   t r u e ; 
         } 
         
         f u n c t i o n   o n D r o p ( s o u r c e ,   t a r g e t ,   p i e c e ,   n e w P o s ,   o l d P o s ,   o r i e n t a t i o n )   { 
                 / /   R e m o v e   h i g h l i g h t s 
                 r e m o v e H i g h l i g h t i n g ( ) ; 
                 
                 / /   S e e   i f   t h e   m o v e   i s   l e g a l 
                 c o n s t   m o v e   =   g a m e . m o v e ( { 
                         f r o m :   s o u r c e , 
                         t o :   t a r g e t , 
                         p r o m o t i o n :   ' q '   / /   A l w a y s   p r o m o t e   t o   q u e e n   f o r   s i m p l i c i t y 
                 } ) ; 
                 
                 / /   I l l e g a l   m o v e 
                 i f   ( m o v e   = = =   n u l l )   r e t u r n   ' s n a p b a c k ' ; 
                 
                 / /   C h e c k   f o r   c a p t u r e s 
                 c h e c k F o r C a p t u r e ( m o v e ) ; 
                 
                 / /   H i g h l i g h t   l a s t   m o v e 
                 h i g h l i g h t L a s t M o v e ( s o u r c e ,   t a r g e t ) ; 
                 l a s t M o v e S o u r c e   =   s o u r c e ; 
                 l a s t M o v e T a r g e t   =   t a r g e t ; 
                 
                 / /   A d d   m o v e   t o   h i s t o r y   a n d   u p d a t e   g a m e   s t a t u s 
                 a d d M o v e T o H i s t o r y ( m o v e ) ; 
                 u p d a t e M o v e L i s t ( ) ; 
                 u p d a t e G a m e S t a t u s ( ) ; 
                 
                 / /   U p d a t e   t u r n   i n d i c a t o r 
                 u p d a t e T u r n I n d i c a t o r ( ) ; 
                 
                 / /   S t a r t   A I   t h i n k i n g 
                 i s P l a y e r T u r n   =   f a l s e ; 
                 s e t T i m e o u t ( m a k e A I M o v e ,   5 0 0 ) ; 
         } 
         
         f u n c t i o n   o n S n a p E n d ( )   { 
                 b o a r d . p o s i t i o n ( g a m e . f e n ( ) ) ; 
         } 
         
         f u n c t i o n   m a k e A I M o v e ( )   { 
                 i f   ( g a m e . g a m e _ o v e r ( ) )   { 
                         i s P l a y e r T u r n   =   f a l s e ; 
                         u p d a t e T u r n I n d i c a t o r ( ) ; 
                         u p d a t e G a m e S t a t u s ( ) ; 
                         r e t u r n ; 
                 } 
                 
                 / /   G e t   b e s t   m o v e   f r o m   e n g i n e 
                 c o n s t   a i M o v e   =   e n g i n e . g e t B e s t M o v e ( g a m e ,   p l a y e r C o l o r   = = =   ' w '   ?   ' b '   :   ' w ' ) ; 
                 
                 i f   ( a i M o v e )   { 
                         / /   H i g h l i g h t   s o u r c e   s q u a r e   b e f o r e   m o v e 
                         h i g h l i g h t S q u a r e ( a i M o v e . f r o m ) ; 
                         
                         / /   M a k e   t h e   A I   m o v e 
                         c o n s t   r e s u l t   =   g a m e . m o v e ( a i M o v e ) ; 
                         
                         / /   U p d a t e   b o a r d   p o s i t i o n 
                         b o a r d . p o s i t i o n ( g a m e . f e n ( ) ) ; 
                         
                         / /   H i g h l i g h t   t h e   A I ' s   m o v e 
                         h i g h l i g h t L a s t M o v e ( a i M o v e . f r o m ,   a i M o v e . t o ) ; 
                         
                         / /   C h e c k   f o r   c a p t u r e d   p i e c e s 
                         c h e c k F o r C a p t u r e ( r e s u l t ) ; 
                         
                         / /   A d d   m o v e   t o   h i s t o r y 
                         a d d M o v e T o H i s t o r y ( r e s u l t ) ; 
                         
                         / /   U p d a t e   g a m e   s t a t u s 
                         u p d a t e G a m e S t a t u s ( ) ; 
                 } 
                 
                 / /   P l a y e r ' s   t u r n   a g a i n 
                 i s P l a y e r T u r n   =   t r u e ; 
                 
                 / /   U p d a t e   t u r n   i n d i c a t o r 
                 u p d a t e T u r n I n d i c a t o r ( ) ; 
         } 
         
         f u n c t i o n   u p d a t e T u r n I n d i c a t o r ( )   { 
                 i f   ( i s P l a y e r T u r n )   { 
                         p l a y e r T u r n E l e m e n t . c l a s s L i s t . a d d ( ' a c t i v e ' ) ; 
                         a i T u r n E l e m e n t . c l a s s L i s t . r e m o v e ( ' a c t i v e ' ) ; 
                 }   e l s e   { 
                         p l a y e r T u r n E l e m e n t . c l a s s L i s t . r e m o v e ( ' a c t i v e ' ) ; 
                         a i T u r n E l e m e n t . c l a s s L i s t . a d d ( ' a c t i v e ' ) ; 
                 } 
         } 
         
         f u n c t i o n   r e m o v e H i g h l i g h t i n g ( )   { 
                 $ b o a r d E l . f i n d ( ' . s q u a r e - 5 5 d 6 3 ' ) . r e m o v e C l a s s ( ' h i g h l i g h t - s q u a r e ' ) ; 
                 $ b o a r d E l . f i n d ( ' . s q u a r e - 5 5 d 6 3 ' ) . r e m o v e C l a s s ( ' h i g h l i g h t - t a r g e t ' ) ; 
                 $ b o a r d E l . f i n d ( ' . s q u a r e - 5 5 d 6 3 ' ) . r e m o v e C l a s s ( ' h i g h l i g h t - l a s t - m o v e ' ) ; 
         } 
         
         f u n c t i o n   h i g h l i g h t S q u a r e ( s q u a r e )   { 
                 $ b o a r d E l . f i n d ( ` . s q u a r e - $ { s q u a r e } ` ) . a d d C l a s s ( ' h i g h l i g h t - s q u a r e ' ) ; 
         } 
         
         f u n c t i o n   h i g h l i g h t L a s t M o v e ( s o u r c e ,   t a r g e t )   { 
                 r e m o v e H i g h l i g h t i n g ( ) ; 
                 h i g h l i g h t S q u a r e ( s o u r c e ) ; 
                 h i g h l i g h t S q u a r e ( t a r g e t ) ; 
                 l a s t M o v e S o u r c e   =   s o u r c e ; 
                 l a s t M o v e T a r g e t   =   t a r g e t ; 
         } 
         
         f u n c t i o n   c h e c k F o r C a p t u r e ( m o v e )   { 
                 i f   ( m o v e . c a p t u r e d )   { 
                         c o n s t   c o l o r   =   m o v e . c o l o r   = = =   ' w '   ?   ' b '   :   ' w ' ; 
                         c a p t u r e d P i e c e s [ c o l o r ] . p u s h ( m o v e . c a p t u r e d ) ; 
                         u p d a t e C a p t u r e d P i e c e s ( ) ; 
                 } 
         } 
         
         f u n c t i o n   u p d a t e C a p t u r e d P i e c e s ( )   { 
                 w h i t e C a p t u r e d E l e m e n t . i n n e r H T M L   =   ' ' ; 
                 b l a c k C a p t u r e d E l e m e n t . i n n e r H T M L   =   ' ' ; 
                 
                 c a p t u r e d P i e c e s . b . f o r E a c h ( p i e c e   = >   { 
                         c o n s t   p i e c e I c o n   =   g e t P i e c e I c o n ( ' b ' ,   p i e c e ) ; 
                         w h i t e C a p t u r e d E l e m e n t . i n n e r H T M L   + =   p i e c e I c o n ; 
                 } ) ; 
                 
                 c a p t u r e d P i e c e s . w . f o r E a c h ( p i e c e   = >   { 
                         c o n s t   p i e c e I c o n   =   g e t P i e c e I c o n ( ' w ' ,   p i e c e ) ; 
                         b l a c k C a p t u r e d E l e m e n t . i n n e r H T M L   + =   p i e c e I c o n ; 
                 } ) ; 
         } 
         
         f u n c t i o n   g e t P i e c e I c o n ( c o l o r ,   p i e c e )   { 
                 c o n s t   c o l o r C o d e   =   c o l o r   = = =   ' w '   ?   ' w h i t e '   :   ' b l a c k ' ; 
                 l e t   p i e c e N a m e   =   ' ' ; 
                 
                 s w i t c h ( p i e c e )   { 
                         c a s e   ' p ' :   p i e c e N a m e   =   ' p a w n ' ;   b r e a k ; 
                         c a s e   ' n ' :   p i e c e N a m e   =   ' k n i g h t ' ;   b r e a k ; 
                         c a s e   ' b ' :   p i e c e N a m e   =   ' b i s h o p ' ;   b r e a k ; 
                         c a s e   ' r ' :   p i e c e N a m e   =   ' r o o k ' ;   b r e a k ; 
                         c a s e   ' q ' :   p i e c e N a m e   =   ' q u e e n ' ;   b r e a k ; 
                         c a s e   ' k ' :   p i e c e N a m e   =   ' k i n g ' ;   b r e a k ; 
                 } 
                 
                 r e t u r n   ` < d i v   c l a s s = " c a p t u r e d - p i e c e " > < s p a n   c l a s s = " p i e c e - i c o n   $ { c o l o r C o d e } - $ { p i e c e } " > & # $ { g e t P i e c e U n i c o d e ( c o l o r ,   p i e c e ) } ; < / s p a n > < / d i v > ` ; 
         } 
         
         f u n c t i o n   g e t P i e c e U n i c o d e ( c o l o r ,   p i e c e )   { 
                 c o n s t   p i e c e s   =   { 
                         ' w ' :   { 
                                 ' p ' :   ' 9 8 1 7 ' , 
                                 ' n ' :   ' 9 8 1 6 ' , 
                                 ' b ' :   ' 9 8 1 5 ' , 
                                 ' r ' :   ' 9 8 1 4 ' , 
                                 ' q ' :   ' 9 8 1 3 ' , 
                                 ' k ' :   ' 9 8 1 2 ' 
                         } , 
                         ' b ' :   { 
                                 ' p ' :   ' 9 8 2 3 ' , 
                                 ' n ' :   ' 9 8 2 2 ' , 
                                 ' b ' :   ' 9 8 2 1 ' , 
                                 ' r ' :   ' 9 8 2 0 ' , 
                                 ' q ' :   ' 9 8 1 9 ' , 
                                 ' k ' :   ' 9 8 1 8 ' 
                         } 
                 } ; 
                 
                 r e t u r n   p i e c e s [ c o l o r ] [ p i e c e ] ; 
         } 
         
         f u n c t i o n   a d d M o v e T o H i s t o r y ( m o v e )   { 
                 m o v e H i s t o r y . p u s h ( m o v e ) ; 
                 u p d a t e M o v e L i s t ( ) ; 
         } 
         
         f u n c t i o n   u p d a t e M o v e L i s t ( )   { 
                 m o v e L i s t E l e m e n t . i n n e r H T M L   =   ' ' ; 
                 
                 f o r   ( l e t   i   =   0 ;   i   <   m o v e H i s t o r y . l e n g t h ;   i   + =   2 )   { 
                         c o n s t   m o v e N u m b e r   =   M a t h . f l o o r ( i   /   2 )   +   1 ; 
                         c o n s t   w h i t e M o v e   =   m o v e H i s t o r y [ i ]   ?   m o v e H i s t o r y [ i ] . s a n   :   ' ' ; 
                         c o n s t   b l a c k M o v e   =   m o v e H i s t o r y [ i   +   1 ]   ?   m o v e H i s t o r y [ i   +   1 ] . s a n   :   ' ' ; 
                         
                         c o n s t   m o v e P a i r   =   d o c u m e n t . c r e a t e E l e m e n t ( ' d i v ' ) ; 
                         m o v e P a i r . c l a s s N a m e   =   ' m o v e - p a i r ' ; 
                         
                         c o n s t   m o v e N u m E l e m e n t   =   d o c u m e n t . c r e a t e E l e m e n t ( ' d i v ' ) ; 
                         m o v e N u m E l e m e n t . c l a s s N a m e   =   ' m o v e - n u m b e r ' ; 
                         m o v e N u m E l e m e n t . t e x t C o n t e n t   =   m o v e N u m b e r   +   ' . ' ; 
                         
                         c o n s t   w h i t e M o v e E l e m e n t   =   d o c u m e n t . c r e a t e E l e m e n t ( ' d i v ' ) ; 
                         w h i t e M o v e E l e m e n t . c l a s s N a m e   =   ' m o v e - w h i t e ' ; 
                         w h i t e M o v e E l e m e n t . t e x t C o n t e n t   =   w h i t e M o v e ; 
                         
                         c o n s t   b l a c k M o v e E l e m e n t   =   d o c u m e n t . c r e a t e E l e m e n t ( ' d i v ' ) ; 
                         b l a c k M o v e E l e m e n t . c l a s s N a m e   =   ' m o v e - b l a c k ' ; 
                         b l a c k M o v e E l e m e n t . t e x t C o n t e n t   =   b l a c k M o v e ; 
                         
                         m o v e P a i r . a p p e n d C h i l d ( m o v e N u m E l e m e n t ) ; 
                         m o v e P a i r . a p p e n d C h i l d ( w h i t e M o v e E l e m e n t ) ; 
                         m o v e P a i r . a p p e n d C h i l d ( b l a c k M o v e E l e m e n t ) ; 
                         
                         m o v e L i s t E l e m e n t . a p p e n d C h i l d ( m o v e P a i r ) ; 
                 } 
                 
                 / /   S c r o l l   t o   b o t t o m   o f   m o v e   l i s t 
                 m o v e L i s t E l e m e n t . s c r o l l T o p   =   m o v e L i s t E l e m e n t . s c r o l l H e i g h t ; 
         } 
         
         f u n c t i o n   u p d a t e G a m e S t a t u s ( )   { 
                 l e t   s t a t u s T e x t   =   ' ' ; 
                 
                 i f   ( g a m e . i n _ c h e c k m a t e ( ) )   { 
                         s t a t u s T e x t   =   ' G a m e   o v e r ,   '   +   ( g a m e . t u r n ( )   = = =   ' w '   ?   ' b l a c k '   :   ' w h i t e ' )   +   '   i s   v i c t o r i o u s ! ' ; 
                 }   e l s e   i f   ( g a m e . i n _ d r a w ( ) )   { 
                         s t a t u s T e x t   =   ' G a m e   o v e r ,   d r a w n   p o s i t i o n ' ; 
                 }   e l s e   i f   ( g a m e . i n _ c h e c k ( ) )   { 
                         s t a t u s T e x t   =   ( g a m e . t u r n ( )   = = =   ' w '   ?   ' W h i t e '   :   ' B l a c k ' )   +   '   i s   i n   c h e c k ! ' ; 
                 }   e l s e   { 
                         s t a t u s T e x t   =   i s P l a y e r T u r n   ?   ' Y o u r   t u r n '   :   ' A I   i s   t h i n k i n g . . . ' ; 
                 } 
                 
                 u p d a t e S t a t u s ( s t a t u s T e x t ) ; 
         } 
         
         f u n c t i o n   u p d a t e S t a t u s ( m e s s a g e )   { 
                 s t a t u s E l e m e n t . t e x t C o n t e n t   =   m e s s a g e ; 
         } 
         
         f u n c t i o n   c a l c u l a t e C a p t u r e d P i e c e s ( g a m e )   { 
                 c o n s t   c a p t u r e d P i e c e s   =   {   w :   [ ] ,   b :   [ ]   } ; 
                 c o n s t   h i s t o r y   =   g a m e . h i s t o r y ( {   v e r b o s e :   t r u e   } ) ; 
                 
                 h i s t o r y . f o r E a c h ( m o v e   = >   { 
                         i f   ( m o v e . c a p t u r e d )   { 
                                 c o n s t   c o l o r   =   m o v e . c o l o r   = = =   ' w '   ?   ' b '   :   ' w ' ; 
                                 c a p t u r e d P i e c e s [ c o l o r ] . p u s h ( m o v e . c a p t u r e d ) ; 
                         } 
                 } ) ; 
                 
                 r e t u r n   c a p t u r e d P i e c e s ; 
         } 
         
         / /   = = = = =   M U L T I P L A Y E R   M O D E   = = = = = 
         
         / /   C o n n e c t i o n   s e t u p   e v e n t   l i s t e n e r s 
         c r e a t e B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 c o n n e c t i o n S e t u p . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 m o d e S e l e c t o r . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 c r e a t e G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 j o i n G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 s t a t u s T e x t . t e x t C o n t e n t   =   ' S e t t i n g   u p   g a m e . . . ' ; 
                 c o p y C o d e B t n . d i s a b l e d   =   t r u e ; 
                 i n i t i a l i z e P e e r ( ) ; 
         } ) ; 
         
         j o i n B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 c o n n e c t i o n S e t u p . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 m o d e S e l e c t o r . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 c r e a t e G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 j o i n G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' S e t t i n g   u p   c o n n e c t i o n . . . ' ; 
                 c o n n e c t B t n . d i s a b l e d   =   t r u e ; 
                 i n i t i a l i z e P e e r ( ) ; 
         } ) ; 
         
         c o n n e c t B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 c o n s t   c o d e   =   g a m e C o d e I n p u t . v a l u e . t r i m ( ) . t o U p p e r C a s e ( ) ; 
                 i f   ( c o d e   & &   c o d e . l e n g t h   > =   4 )   { 
                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ` C o n n e c t i n g   t o   g a m e   $ { c o d e } . . . ` ; 
                         c o n n e c t B t n . d i s a b l e d   =   t r u e ; 
                         c o n n e c t T o P e e r ( c o d e ) ; 
                 }   e l s e   { 
                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' P l e a s e   e n t e r   a   v a l i d   g a m e   c o d e ' ; 
                 } 
         } ) ; 
         
         c o p y C o d e B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 c o n s t   g a m e C o d e   =   g a m e C o d e D i s p l a y . v a l u e . t r i m ( ) ; 
                 
                 i f   ( ! g a m e C o d e )   { 
                         s t a t u s T e x t . t e x t C o n t e n t   =   ' N o   g a m e   c o d e   a v a i l a b l e   t o   c o p y ' ; 
                         r e t u r n ; 
                 } 
                 
                 / /   U s e   m o d e r n   c l i p b o a r d   A P I   w i t h   f a l l b a c k 
                 i f   ( n a v i g a t o r . c l i p b o a r d   & &   n a v i g a t o r . c l i p b o a r d . w r i t e T e x t )   { 
                         n a v i g a t o r . c l i p b o a r d . w r i t e T e x t ( g a m e C o d e ) 
                                 . t h e n ( ( )   = >   { 
                                         s t a t u s T e x t . t e x t C o n t e n t   =   ' G a m e   c o d e   c o p i e d   t o   c l i p b o a r d ! ' ; 
                                         s e t T i m e o u t ( ( )   = >   { 
                                                 s t a t u s T e x t . t e x t C o n t e n t   =   ' S h a r e   t h i s   c o d e   w i t h   y o u r   o p p o n e n t ' ; 
                                         } ,   2 0 0 0 ) ; 
                                 } ) 
                                 . c a t c h ( e r r   = >   { 
                                         c o n s o l e . e r r o r ( ' F a i l e d   t o   c o p y   c o d e :   ' ,   e r r ) ; 
                                         f a l l b a c k C o p y ( ) ; 
                                 } ) ; 
                 }   e l s e   { 
                         f a l l b a c k C o p y ( ) ; 
                 } 
                 
                 f u n c t i o n   f a l l b a c k C o p y ( )   { 
                         / /   S e l e c t   t h e   t e x t 
                         g a m e C o d e D i s p l a y . s e l e c t ( ) ; 
                         g a m e C o d e D i s p l a y . s e t S e l e c t i o n R a n g e ( 0 ,   9 9 9 9 9 ) ;   / /   F o r   m o b i l e   d e v i c e s 
                         
                         t r y   { 
                                 / /   E x e c u t e   c o p y   c o m m a n d 
                                 c o n s t   s u c c e s s f u l   =   d o c u m e n t . e x e c C o m m a n d ( ' c o p y ' ) ; 
                                 i f   ( s u c c e s s f u l )   { 
                                         s t a t u s T e x t . t e x t C o n t e n t   =   ' G a m e   c o d e   c o p i e d   t o   c l i p b o a r d ! ' ; 
                                         s e t T i m e o u t ( ( )   = >   { 
                                                 s t a t u s T e x t . t e x t C o n t e n t   =   ' S h a r e   t h i s   c o d e   w i t h   y o u r   o p p o n e n t ' ; 
                                         } ,   2 0 0 0 ) ; 
                                 }   e l s e   { 
                                         s t a t u s T e x t . t e x t C o n t e n t   =   ' P l e a s e   s e l e c t   a n d   c o p y   t h e   c o d e   m a n u a l l y ' ; 
                                 } 
                         }   c a t c h   ( e r r )   { 
                                 c o n s o l e . e r r o r ( ' F a l l b a c k   c o p y   f a i l e d : ' ,   e r r ) ; 
                                 s t a t u s T e x t . t e x t C o n t e n t   =   ' P l e a s e   s e l e c t   a n d   c o p y   t h e   c o d e   m a n u a l l y ' ; 
                         } 
                 } 
         } ) ; 
         
         r e s i g n B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 i f   ( c o n n e c t i o n   & &   c o n n e c t i o n . o p e n )   { 
                         c o n n e c t i o n . s e n d ( { 
                                 t y p e :   ' r e s i g n ' 
                         } ) ; 
                         m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' Y o u   r e s i g n e d .   G a m e   o v e r . ' ; 
                         d i s a b l e M p B o a r d ( ) ; 
                 } 
         } ) ; 
         
         e x i t M p B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 e x i t M u l t i p l a y e r G a m e ( ) ; 
         } ) ; 
         
         / /   I n i t i a l i z e   P e e r J S   f o r   m u l t i p l a y e r 
         f u n c t i o n   i n i t i a l i z e P e e r ( )   { 
                 t r y   { 
                         / /   G e n e r a t e   a   r a n d o m   I D   i f   c r e a t i n g   a   g a m e 
                         c o n s t   i s C r e a t i n g G a m e   =   c r e a t e G a m e S e c t i o n . s t y l e . d i s p l a y   = = =   ' b l o c k ' ; 
                         
                         i f   ( i s C r e a t i n g G a m e )   { 
                                 / /   G e n e r a t e   a   r a n d o m   6 - c h a r a c t e r   g a m e   c o d e 
                                 c o n s t   g a m e C o d e   =   g e n e r a t e G a m e C o d e ( ) ; 
                                 c o n s o l e . l o g ( " G e n e r a t e d   g a m e   c o d e : " ,   g a m e C o d e ) ; 
                                 
                                 / /   C r e a t e   a   n e w   p e e r   w i t h   t h e   g e n e r a t e d   I D 
                                 p e e r   =   n e w   P e e r ( g a m e C o d e ,   { 
                                         d e b u g :   2 
                                 } ) ; 
                         }   e l s e   { 
                                 / /   C r e a t e   a   p e e r   w i t h   a   r a n d o m   I D   f o r   j o i n i n g   a   g a m e 
                                 p e e r   =   n e w   P e e r ( { 
                                         d e b u g :   2 
                                 } ) ; 
                         } 
                         
                         / /   H a n d l e   s u c c e s s f u l   c o n n e c t i o n   t o   t h e   P e e r J S   s e r v e r 
                         p e e r . o n ( ' o p e n ' ,   ( i d )   = >   { 
                                 c o n s o l e . l o g ( " C o n n e c t e d   t o   P e e r J S   s e r v e r   w i t h   I D : " ,   i d ) ; 
                                 
                                 i f   ( i s C r e a t i n g G a m e )   { 
                                         / /   D i s p l a y   t h e   g a m e   c o d e   f o r   t h e   h o s t   t o   s h a r e 
                                         g a m e C o d e D i s p l a y . v a l u e   =   i d ; 
                                         s t a t u s T e x t . t e x t C o n t e n t   =   ' G a m e   c r e a t e d !   S h a r e   t h e   c o d e   a b o v e   w i t h   y o u r   o p p o n e n t . ' ; 
                                         c o p y C o d e B t n . d i s a b l e d   =   f a l s e ; 
                                 }   e l s e   { 
                                         c o n s o l e . l o g ( " R e a d y   t o   j o i n   a   g a m e " ) ; 
                                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' R e a d y   t o   j o i n   a   g a m e ' ; 
                                         c o n n e c t B t n . d i s a b l e d   =   f a l s e ; 
                                 } 
                         } ) ; 
                         
                         / /   L i s t e n   f o r   i n c o m i n g   c o n n e c t i o n s   i f   c r e a t i n g   a   g a m e 
                         i f   ( i s C r e a t i n g G a m e )   { 
                                 p e e r . o n ( ' c o n n e c t i o n ' ,   ( c o n n )   = >   { 
                                         c o n s o l e . l o g ( " I n c o m i n g   c o n n e c t i o n   f r o m : " ,   c o n n . p e e r ) ; 
                                         s t a t u s T e x t . t e x t C o n t e n t   =   ' O p p o n e n t   i s   c o n n e c t i n g . . . ' ; 
                                         
                                         / /   O n l y   a c c e p t   o n e   c o n n e c t i o n 
                                         i f   ( c o n n e c t i o n   & &   c o n n e c t i o n . o p e n )   { 
                                                 c o n s o l e . l o g ( " R e j e c t i n g   a d d i t i o n a l   c o n n e c t i o n   a t t e m p t " ) ; 
                                                 c o n n . c l o s e ( ) ; 
                                                 r e t u r n ; 
                                         } 
                                         
                                         c o n n e c t i o n   =   c o n n ; 
                                         
                                         c o n n e c t i o n . o n ( ' o p e n ' ,   ( )   = >   { 
                                                 c o n s o l e . l o g ( " C o n n e c t i o n   e s t a b l i s h e d   w i t h : " ,   c o n n e c t i o n . p e e r ) ; 
                                                 s t a t u s T e x t . t e x t C o n t e n t   =   ' O p p o n e n t   c o n n e c t e d !   G a m e   s t a r t i n g . . . ' ; 
                                                 s e t u p C o n n e c t i o n H a n d l e r s ( ) ; 
                                                 
                                                 / /   S w i t c h   t o   g a m e   v i e w 
                                                 s e t T i m e o u t ( ( )   = >   { 
                                                         c r e a t e G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                                                         c o n n e c t i o n S e t u p . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                                                         m u l t i p l a y e r G a m e . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                                                         
                                                         / /   I n i t i a l i z e   m u l t i p l a y e r   g a m e   a s   w h i t e 
                                                         m p P l a y e r S i d e   =   ' w ' ; 
                                                         i n i t i a l i z e M u l t i p l a y e r G a m e ( ) ; 
                                                 } ,   1 0 0 0 ) ; 
                                         } ) ; 
                                 } ) ; 
                         } 
                         
                         / /   H a n d l e   c o n n e c t i o n   e r r o r s 
                         p e e r . o n ( ' e r r o r ' ,   ( e r r )   = >   { 
                                 c o n s o l e . e r r o r ( " P e e r J S   e r r o r : " ,   e r r ) ; 
                                 
                                 i f   ( e r r . t y p e   = = =   ' u n a v a i l a b l e - i d ' )   { 
                                         / /   I f   c r e a t i n g   a   g a m e   a n d   t h e   I D   i s   t a k e n 
                                         i f   ( i s C r e a t i n g G a m e )   { 
                                                 s t a t u s T e x t . t e x t C o n t e n t   =   ' G a m e   c o d e   a l r e a d y   i n   u s e .   T r y   a g a i n . ' ; 
                                                 / /   T r y   a g a i n   w i t h   a   n e w   c o d e 
                                                 s e t T i m e o u t ( i n i t i a l i z e P e e r ,   1 0 0 0 ) ; 
                                         } 
                                 }   e l s e   i f   ( e r r . t y p e   = = =   ' p e e r - u n a v a i l a b l e ' )   { 
                                         / /   I f   j o i n i n g   a n d   t h e   p e e r   i s   n o t   a v a i l a b l e 
                                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' G a m e   n o t   f o u n d .   C h e c k   t h e   c o d e   a n d   t r y   a g a i n . ' ; 
                                         c o n n e c t B t n . d i s a b l e d   =   f a l s e ; 
                                 }   e l s e   { 
                                         / /   G e n e r i c   e r r o r   h a n d l i n g 
                                         c o n s t   m e s s a g e   =   i s C r e a t i n g G a m e   ?   s t a t u s T e x t   :   j o i n S t a t u s T e x t ; 
                                         m e s s a g e . t e x t C o n t e n t   =   ' C o n n e c t i o n   e r r o r :   '   +   e r r . m e s s a g e ; 
                                 } 
                         } ) ; 
                         
                         / /   H a n d l e   d i s c o n n e c t i o n   f r o m   t h e   P e e r J S   s e r v e r 
                         p e e r . o n ( ' d i s c o n n e c t e d ' ,   ( )   = >   { 
                                 c o n s o l e . l o g ( " D i s c o n n e c t e d   f r o m   P e e r J S   s e r v e r " ) ; 
                                 c o n s t   m e s s a g e   =   i s C r e a t i n g G a m e   ?   s t a t u s T e x t   :   j o i n S t a t u s T e x t ; 
                                 m e s s a g e . t e x t C o n t e n t   =   ' D i s c o n n e c t e d   f r o m   s e r v e r .   R e c o n n e c t i n g . . . ' ; 
                                 
                                 / /   T r y   t o   r e c o n n e c t 
                                 p e e r . r e c o n n e c t ( ) ; 
                         } ) ; 
                         
                 }   c a t c h   ( e )   { 
                         c o n s o l e . e r r o r ( " E r r o r   i n i t i a l i z i n g   P e e r J S : " ,   e ) ; 
                         c o n s t   m e s s a g e   =   c r e a t e G a m e S e c t i o n . s t y l e . d i s p l a y   = = =   ' b l o c k '   ?   s t a t u s T e x t   :   j o i n S t a t u s T e x t ; 
                         m e s s a g e . t e x t C o n t e n t   =   ' C o n n e c t i o n   e r r o r :   '   +   e . m e s s a g e ; 
                 } 
         } 
         
         / /   G e n e r a t e   a   r a n d o m   g a m e   c o d e   ( 6   u p p e r c a s e   a l p h a n u m e r i c   c h a r a c t e r s ) 
         f u n c t i o n   g e n e r a t e G a m e C o d e ( )   { 
                 c o n s t   c h a r s   =   ' A B C D E F G H J K L M N P Q R S T U V W X Y Z 2 3 4 5 6 7 8 9 ' ;   / /   O m i t t i n g   s i m i l a r   l o o k i n g   c h a r s 
                 l e t   r e s u l t   =   ' ' ; 
                 f o r   ( l e t   i   =   0 ;   i   <   6 ;   i + + )   { 
                         r e s u l t   + =   c h a r s . c h a r A t ( M a t h . f l o o r ( M a t h . r a n d o m ( )   *   c h a r s . l e n g t h ) ) ; 
                 } 
                 r e t u r n   r e s u l t ; 
         } 
         
         / /   S e t u p   e v e n t   h a n d l e r s   f o r   t h e   d a t a   c o n n e c t i o n 
         f u n c t i o n   s e t u p C o n n e c t i o n H a n d l e r s ( )   { 
                 i f   ( ! c o n n e c t i o n )   { 
                         c o n s o l e . e r r o r ( " N o   c o n n e c t i o n   a v a i l a b l e   t o   s e t u p   h a n d l e r s " ) ; 
                         r e t u r n ; 
                 } 
                 
                 / /   H a n d l e   i n c o m i n g   g a m e   d a t a 
                 c o n n e c t i o n . o n ( ' d a t a ' ,   f u n c t i o n ( d a t a )   { 
                         c o n s o l e . l o g ( " R e c e i v e d   d a t a : " ,   d a t a ) ; 
                         h a n d l e G a m e D a t a ( d a t a ) ; 
                 } ) ; 
                 
                 c o n n e c t i o n . o n ( ' c l o s e ' ,   f u n c t i o n ( )   { 
                         c o n s o l e . l o g ( " C o n n e c t i o n   c l o s e d   b y   p e e r " ) ; 
                         m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' O p p o n e n t   d i s c o n n e c t e d ' ; 
                         d i s a b l e M p B o a r d ( ) ; 
                 } ) ; 
                 
                 c o n n e c t i o n . o n ( ' e r r o r ' ,   f u n c t i o n ( e r r )   { 
                         c o n s o l e . e r r o r ( ' C o n n e c t i o n   e r r o r : ' ,   e r r ) ; 
                         m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' C o n n e c t i o n   e r r o r :   '   +   e r r . m e s s a g e ; 
                 } ) ; 
         } 
         
         / /   H a n d l e   i n c o m i n g   g a m e   d a t a 
         f u n c t i o n   h a n d l e G a m e D a t a ( d a t a )   { 
                 t r y   { 
                         c o n s o l e . l o g ( ' P r o c e s s i n g   g a m e   d a t a : ' ,   d a t a ) ; 
                         
                         i f   ( ! d a t a   | |   ! d a t a . t y p e )   { 
                                 c o n s o l e . e r r o r ( " I n v a l i d   d a t a   r e c e i v e d : " ,   d a t a ) ; 
                                 r e t u r n ; 
                         } 
                         
                         s w i t c h   ( d a t a . t y p e )   { 
                                 c a s e   ' g a m e - i n i t ' : 
                                         c o n s o l e . l o g ( " I n i t i a l i z i n g   g a m e   a s : " ,   d a t a . p l a y e r S i d e ) ; 
                                         m p P l a y e r S i d e   =   d a t a . p l a y e r S i d e ; 
                                         i n i t i a l i z e M u l t i p l a y e r G a m e ( ) ; 
                                         b r e a k ; 
                                         
                                 c a s e   ' m o v e ' : 
                                         i f   ( ! d a t a . f r o m   | |   ! d a t a . t o )   { 
                                                 c o n s o l e . e r r o r ( " I n v a l i d   m o v e   d a t a : " ,   d a t a ) ; 
                                                 r e t u r n ; 
                                         } 
                                         
                                         c o n s o l e . l o g ( ` P r o c e s s i n g   o p p o n e n t   m o v e :   $ { d a t a . f r o m }   t o   $ { d a t a . t o } ` ) ; 
                                         
                                         / /   M a k e   t h e   o p p o n e n t ' s   m o v e 
                                         c o n s t   r e s u l t   =   m p G a m e . m o v e ( { 
                                                 f r o m :   d a t a . f r o m , 
                                                 t o :   d a t a . t o , 
                                                 p r o m o t i o n :   d a t a . p r o m o t i o n   | |   ' q ' 
                                         } ) ; 
                                         
                                         i f   ( ! r e s u l t )   { 
                                                 c o n s o l e . e r r o r ( " I n v a l i d   m o v e   a t t e m p t e d : " ,   d a t a ) ; 
                                                 r e t u r n ; 
                                         } 
                                         
                                         / /   U p d a t e   b o a r d 
                                         m p B o a r d . p o s i t i o n ( m p G a m e . f e n ( ) ) ; 
                                         
                                         / /   H i g h l i g h t   t h e   m o v e 
                                         h i g h l i g h t M p L a s t M o v e ( d a t a . f r o m ,   d a t a . t o ) ; 
                                         
                                         / /   C h e c k   f o r   c a p t u r e d   p i e c e s 
                                         c h e c k M p F o r C a p t u r e ( r e s u l t ) ; 
                                         
                                         / /   A d d   m o v e   t o   h i s t o r y 
                                         a d d M p M o v e T o H i s t o r y ( r e s u l t ) ; 
                                         
                                         / /   U p d a t e   s t a t u s 
                                         u p d a t e M p G a m e S t a t u s ( ) ; 
                                         b r e a k ; 
                                         
                                 c a s e   ' r e s i g n ' : 
                                         c o n s o l e . l o g ( " O p p o n e n t   r e s i g n e d " ) ; 
                                         m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' O p p o n e n t   r e s i g n e d .   Y o u   w i n ! ' ; 
                                         d i s a b l e M p B o a r d ( ) ; 
                                         b r e a k ; 
                                         
                                 d e f a u l t : 
                                         c o n s o l e . w a r n ( " U n k n o w n   d a t a   t y p e   r e c e i v e d : " ,   d a t a . t y p e ) ; 
                         } 
                 }   c a t c h   ( e )   { 
                         c o n s o l e . e r r o r ( " E r r o r   h a n d l i n g   g a m e   d a t a : " ,   e ) ; 
                         m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' E r r o r   p r o c e s s i n g   g a m e   d a t a ' ; 
                 } 
         } 
         
         / /   S e n d   g a m e   d a t a   t o   t h e   p e e r 
         f u n c t i o n   s e n d G a m e D a t a ( d a t a )   { 
                 i f   ( c o n n e c t i o n   & &   c o n n e c t i o n . o p e n )   { 
                         t r y   { 
                                 c o n s o l e . l o g ( " S e n d i n g   d a t a : " ,   d a t a ) ; 
                                 c o n n e c t i o n . s e n d ( d a t a ) ; 
                         }   c a t c h   ( e )   { 
                                 c o n s o l e . e r r o r ( " E r r o r   s e n d i n g   d a t a : " ,   e ) ; 
                                 m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' E r r o r   s e n d i n g   m o v e ' ; 
                         } 
                 }   e l s e   { 
                         c o n s o l e . e r r o r ( " C a n n o t   s e n d   d a t a   -   c o n n e c t i o n   n o t   o p e n " ) ; 
                         m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' C o n n e c t i o n   l o s t ' ; 
                 } 
         } 
         
         / /   F u n c t i o n s   f o r   m u l t i p l a y e r   m o d e 
         f u n c t i o n   o n M p D r a g S t a r t ( s o u r c e ,   p i e c e )   { 
                 / /   D o n ' t   a l l o w   m o v e s   i f   g a m e   i s   o v e r 
                 i f   ( m p G a m e . g a m e _ o v e r ( ) )   r e t u r n   f a l s e ; 
                 
                 / /   O n l y   a l l o w   t h e   p l a y e r   t o   m o v e   t h e i r   o w n   p i e c e s   a n d   o n l y   o n   t h e i r   t u r n 
                 i f   ( ( m p G a m e . t u r n ( )   = = =   ' w '   & &   p i e c e . s e a r c h ( / ^ b / )   ! = =   - 1 )   | | 
                         ( m p G a m e . t u r n ( )   = = =   ' b '   & &   p i e c e . s e a r c h ( / ^ w / )   ! = =   - 1 )   | | 
                         ( m p G a m e . t u r n ( )   = = =   ' w '   & &   m p P l a y e r S i d e   = = =   ' b ' )   | | 
                         ( m p G a m e . t u r n ( )   = = =   ' b '   & &   m p P l a y e r S i d e   = = =   ' w ' ) )   { 
                         r e t u r n   f a l s e ; 
                 } 
                 
                 / /   H i g h l i g h t   v a l i d   t a r g e t   s q u a r e s   i f   o n   d e s k t o p 
                 i f   ( ! i s M o b i l e )   { 
                         c o n s t   m o v e s   =   m p G a m e . m o v e s ( { 
                                 s q u a r e :   s o u r c e , 
                                 v e r b o s e :   t r u e 
                         } ) ; 
                         
                         f o r   ( l e t   m o v e   o f   m o v e s )   { 
                                 $ m p B o a r d E l . f i n d ( ` . s q u a r e - $ { m o v e . t o } ` ) . a d d C l a s s ( ' h i g h l i g h t - t a r g e t ' ) ; 
                         } 
                 } 
                 
                 r e t u r n   t r u e ; 
         } 
         
         f u n c t i o n   o n M p D r o p ( s o u r c e ,   t a r g e t ,   p i e c e ,   n e w P o s ,   o l d P o s ,   o r i e n t a t i o n )   { 
                 / /   R e m o v e   h i g h l i g h t s 
                 r e m o v e M p H i g h l i g h t i n g ( ) ; 
                 
                 / /   S e e   i f   t h e   m o v e   i s   l e g a l 
                 c o n s t   m o v e   =   m p G a m e . m o v e ( { 
                         f r o m :   s o u r c e , 
                         t o :   t a r g e t , 
                         p r o m o t i o n :   ' q '   / /   A l w a y s   p r o m o t e   t o   q u e e n   f o r   s i m p l i c i t y 
                 } ) ; 
                 
                 / /   I l l e g a l   m o v e 
                 i f   ( m o v e   = = =   n u l l )   r e t u r n   ' s n a p b a c k ' ; 
                 
                 / /   C h e c k   f o r   c a p t u r e s 
                 c h e c k M p F o r C a p t u r e ( {   m o v e   } ) ; 
                 
                 / /   H i g h l i g h t   l a s t   m o v e 
                 h i g h l i g h t M p L a s t M o v e ( s o u r c e ,   t a r g e t ) ; 
                 m p L a s t M o v e S o u r c e   =   s o u r c e ; 
                 m p L a s t M o v e T a r g e t   =   t a r g e t ; 
                 
                 / /   A d d   m o v e   t o   h i s t o r y   a n d   u p d a t e   g a m e   s t a t u s 
                 a d d M p M o v e T o H i s t o r y ( m o v e ) ; 
                 u p d a t e M p G a m e S t a t u s ( ) ; 
                 
                 / /   S e n d   m o v e   t o   o p p o n e n t 
                 s e n d G a m e D a t a ( { 
                         t y p e :   ' m o v e ' , 
                         m o v e :   m o v e , 
                         f e n :   m p G a m e . f e n ( ) 
                 } ) ; 
         } 
         
         f u n c t i o n   o n M p S n a p E n d ( )   { 
                 m p B o a r d . p o s i t i o n ( m p G a m e . f e n ( ) ) ; 
         } 
         
         f u n c t i o n   r e m o v e M p H i g h l i g h t i n g ( )   { 
                 $ m p B o a r d E l . f i n d ( ' . s q u a r e - 5 5 d 6 3 ' ) . r e m o v e C l a s s ( ' h i g h l i g h t - s q u a r e ' ) ; 
                 $ m p B o a r d E l . f i n d ( ' . s q u a r e - 5 5 d 6 3 ' ) . r e m o v e C l a s s ( ' h i g h l i g h t - t a r g e t ' ) ; 
                 $ m p B o a r d E l . f i n d ( ' . s q u a r e - 5 5 d 6 3 ' ) . r e m o v e C l a s s ( ' h i g h l i g h t - l a s t - m o v e ' ) ; 
         } 
         
         f u n c t i o n   h i g h l i g h t M p S q u a r e ( s q u a r e )   { 
                 $ m p B o a r d E l . f i n d ( ` . s q u a r e - $ { s q u a r e } ` ) . a d d C l a s s ( ' h i g h l i g h t - s q u a r e ' ) ; 
         } 
         
         f u n c t i o n   h i g h l i g h t M p L a s t M o v e ( s o u r c e ,   t a r g e t )   { 
                 / /   R e m o v e   p r e v i o u s   h i g h l i g h t s 
                 $ ( ' . m p - s q u a r e - h i g h l i g h t ' ) . r e m o v e C l a s s ( ' m p - s q u a r e - h i g h l i g h t ' ) ; 
                 
                 / /   A d d   h i g h l i g h t s   t o   s o u r c e   a n d   t a r g e t   s q u a r e s 
                 $ ( ' # m p - g a m e - b o a r d   . s q u a r e - '   +   s o u r c e ) . a d d C l a s s ( ' m p - s q u a r e - h i g h l i g h t ' ) ; 
                 $ ( ' # m p - g a m e - b o a r d   . s q u a r e - '   +   t a r g e t ) . a d d C l a s s ( ' m p - s q u a r e - h i g h l i g h t ' ) ; 
         } 
         
         f u n c t i o n   c h e c k M p F o r C a p t u r e ( m o v e R e s u l t )   { 
                 i f   ( m o v e R e s u l t . c a p t u r e d )   { 
                         c o n s o l e . l o g ( ` P i e c e   c a p t u r e d :   $ { m o v e R e s u l t . c a p t u r e d }   b y   $ { m o v e R e s u l t . c o l o r } ` ) ; 
                         u p d a t e M p C a p t u r e d P i e c e s ( ) ; 
                 } 
         } 
         
         f u n c t i o n   u p d a t e M p C a p t u r e d P i e c e s ( )   { 
                 / /   G e t   c a p t u r e d   p i e c e s 
                 c o n s t   c a p t u r e d   =   c a l c u l a t e M p C a p t u r e d P i e c e s ( ) ; 
                 
                 / /   U p d a t e   w h i t e ' s   c a p t u r e d   p i e c e s 
                 l e t   w h i t e C a p t u r e d H T M L   =   ' ' ; 
                 c a p t u r e d . b y W h i t e . f o r E a c h ( p i e c e   = >   { 
                         w h i t e C a p t u r e d H T M L   + =   ` < i m g   s r c = " $ { p i e c e T h e m e . r e p l a c e ( ' { p i e c e } ' ,   ' b '   +   p i e c e ) } "   c l a s s = " c a p t u r e d - p i e c e "   a l t = " $ { p i e c e } " > ` ; 
                 } ) ; 
                 m p W h i t e C a p t u r e d . i n n e r H T M L   =   w h i t e C a p t u r e d H T M L ; 
                 
                 / /   U p d a t e   b l a c k ' s   c a p t u r e d   p i e c e s 
                 l e t   b l a c k C a p t u r e d H T M L   =   ' ' ; 
                 c a p t u r e d . b y B l a c k . f o r E a c h ( p i e c e   = >   { 
                         b l a c k C a p t u r e d H T M L   + =   ` < i m g   s r c = " $ { p i e c e T h e m e . r e p l a c e ( ' { p i e c e } ' ,   ' w '   +   p i e c e ) } "   c l a s s = " c a p t u r e d - p i e c e "   a l t = " $ { p i e c e } " > ` ; 
                 } ) ; 
                 m p B l a c k C a p t u r e d . i n n e r H T M L   =   b l a c k C a p t u r e d H T M L ; 
         } 
         
         f u n c t i o n   c a l c u l a t e M p C a p t u r e d P i e c e s ( )   { 
                 c o n s t   f e n   =   m p G a m e . f e n ( ) ; 
                 c o n s t   p i e c e s   =   f e n . s p l i t ( '   ' ) [ 0 ] ; 
                 
                 / /   C o u n t   p i e c e s   o n   t h e   b o a r d 
                 c o n s t   p i e c e C o u n t s   =   { 
                         ' p ' :   0 ,   ' n ' :   0 ,   ' b ' :   0 ,   ' r ' :   0 ,   ' q ' :   0 , 
                         ' P ' :   0 ,   ' N ' :   0 ,   ' B ' :   0 ,   ' R ' :   0 ,   ' Q ' :   0 
                 } ; 
                 
                 f o r   ( l e t   i   =   0 ;   i   <   p i e c e s . l e n g t h ;   i + + )   { 
                         c o n s t   p i e c e   =   p i e c e s [ i ] ; 
                         i f   ( p i e c e C o u n t s . h a s O w n P r o p e r t y ( p i e c e ) )   { 
                                 p i e c e C o u n t s [ p i e c e ] + + ; 
                         } 
                 } 
                 
                 / /   C a l c u l a t e   c a p t u r e d   p i e c e s 
                 c o n s t   c a p t u r e d   =   { 
                         b y W h i t e :   [ ] , 
                         b y B l a c k :   [ ] 
                 } ; 
                 
                 / /   P a w n s   ( 8   f o r   e a c h   s i d e ) 
                 f o r   ( l e t   i   =   0 ;   i   <   8   -   p i e c e C o u n t s [ ' p ' ] ;   i + + )   c a p t u r e d . b y W h i t e . p u s h ( ' p ' ) ; 
                 f o r   ( l e t   i   =   0 ;   i   <   8   -   p i e c e C o u n t s [ ' P ' ] ;   i + + )   c a p t u r e d . b y B l a c k . p u s h ( ' p ' ) ; 
                 
                 / /   K n i g h t s   ( 2   f o r   e a c h   s i d e ) 
                 f o r   ( l e t   i   =   0 ;   i   <   2   -   p i e c e C o u n t s [ ' n ' ] ;   i + + )   c a p t u r e d . b y W h i t e . p u s h ( ' n ' ) ; 
                 f o r   ( l e t   i   =   0 ;   i   <   2   -   p i e c e C o u n t s [ ' N ' ] ;   i + + )   c a p t u r e d . b y B l a c k . p u s h ( ' n ' ) ; 
                 
                 / /   B i s h o p s   ( 2   f o r   e a c h   s i d e ) 
                 f o r   ( l e t   i   =   0 ;   i   <   2   -   p i e c e C o u n t s [ ' b ' ] ;   i + + )   c a p t u r e d . b y W h i t e . p u s h ( ' b ' ) ; 
                 f o r   ( l e t   i   =   0 ;   i   <   2   -   p i e c e C o u n t s [ ' B ' ] ;   i + + )   c a p t u r e d . b y B l a c k . p u s h ( ' b ' ) ; 
                 
                 / /   R o o k s   ( 2   f o r   e a c h   s i d e ) 
                 f o r   ( l e t   i   =   0 ;   i   <   2   -   p i e c e C o u n t s [ ' r ' ] ;   i + + )   c a p t u r e d . b y W h i t e . p u s h ( ' r ' ) ; 
                 f o r   ( l e t   i   =   0 ;   i   <   2   -   p i e c e C o u n t s [ ' R ' ] ;   i + + )   c a p t u r e d . b y B l a c k . p u s h ( ' r ' ) ; 
                 
                 / /   Q u e e n s   ( 1   f o r   e a c h   s i d e ) 
                 f o r   ( l e t   i   =   0 ;   i   <   1   -   p i e c e C o u n t s [ ' q ' ] ;   i + + )   c a p t u r e d . b y W h i t e . p u s h ( ' q ' ) ; 
                 f o r   ( l e t   i   =   0 ;   i   <   1   -   p i e c e C o u n t s [ ' Q ' ] ;   i + + )   c a p t u r e d . b y B l a c k . p u s h ( ' q ' ) ; 
                 
                 r e t u r n   c a p t u r e d ; 
         } 
         
         f u n c t i o n   a d d M p M o v e T o H i s t o r y ( m o v e )   { 
                 / /   C r e a t e   a   n e w   m o v e   i t e m 
                 c o n s t   m o v e I t e m   =   d o c u m e n t . c r e a t e E l e m e n t ( ' d i v ' ) ; 
                 m o v e I t e m . c l a s s N a m e   =   ' m o v e - i t e m ' ; 
                 
                 / /   F o r m a t   t h e   m o v e   a s   S A N   n o t a t i o n   w i t h   n u m b e r 
                 c o n s t   m o v e N u m b e r   =   M a t h . f l o o r ( ( m p G a m e . h i s t o r y ( ) . l e n g t h   +   1 )   /   2 ) ; 
                 c o n s t   i s W h i t e M o v e   =   m p G a m e . h i s t o r y ( ) . l e n g t h   %   2   = = =   1 ; 
                 
                 i f   ( i s W h i t e M o v e )   { 
                         m o v e I t e m . i n n e r H T M L   =   ` $ { m o v e N u m b e r } .   $ { m o v e . s a n } ` ; 
                 }   e l s e   { 
                         / /   F i n d   t h e   p r e v i o u s   m o v e   i t e m   a n d   u p d a t e   i t 
                         i f   ( m p M o v e L i s t E l e m e n t . l a s t C h i l d )   { 
                                 m p M o v e L i s t E l e m e n t . l a s t C h i l d . i n n e r H T M L   + =   `   $ { m o v e . s a n } ` ; 
                                 r e t u r n ; 
                         }   e l s e   { 
                                 / /   F a l l b a c k   i f   t h e r e ' s   n o   p r e v i o u s   m o v e 
                                 m o v e I t e m . i n n e r H T M L   =   ` $ { m o v e N u m b e r } . . .   $ { m o v e . s a n } ` ; 
                         } 
                 } 
                 
                 m p M o v e L i s t E l e m e n t . a p p e n d C h i l d ( m o v e I t e m ) ; 
                 
                 c o n s t   l a s t M o v e I t e m   =   m p M o v e L i s t E l e m e n t . l a s t C h i l d ; 
                         i f   ( l a s t M o v e I t e m )   { 
                         l a s t M o v e I t e m . s c r o l l I n t o V i e w ( ) ; 
                 } 
                 
                 / /   A u t o - s c r o l l   t o   t h e   b o t t o m 
                 m p M o v e L i s t E l e m e n t . s c r o l l T o p   =   m p M o v e L i s t E l e m e n t . s c r o l l H e i g h t ; 
         } 
         
         f u n c t i o n   c l e a r M p M o v e H i s t o r y ( )   { 
                 m p M o v e L i s t E l e m e n t . i n n e r H T M L   =   ' ' ; 
         } 
         
         f u n c t i o n   u p d a t e M p G a m e S t a t u s ( )   { 
                 l e t   s t a t u s T e x t   =   ' ' ; 
                 
                 / /   G e t   t h e   c u r r e n t   t u r n 
                 c o n s t   c u r r e n t T u r n   =   m p G a m e . t u r n ( ) ; 
                 c o n s t   i s P l a y e r s T u r n   =   c u r r e n t T u r n   = = =   m p P l a y e r S i d e ; 
                 
                 / /   C h e c k   f o r   c h e c k m a t e 
                 i f   ( m p G a m e . i n _ c h e c k m a t e ( ) )   { 
                         c o n s t   w i n n e r   =   c u r r e n t T u r n   = = =   ' w '   ?   ' B l a c k '   :   ' W h i t e ' ; 
                         s t a t u s T e x t   =   ` C h e c k m a t e !   $ { w i n n e r }   w i n s ! ` ; 
                         d i s a b l e M p B o a r d ( ) ; 
                 } 
                 / /   C h e c k   f o r   d r a w 
                 e l s e   i f   ( m p G a m e . i n _ d r a w ( ) )   { 
                         s t a t u s T e x t   =   ' G a m e   e n d e d   i n   d r a w ! ' ; 
                         d i s a b l e M p B o a r d ( ) ; 
                 } 
                 / /   C h e c k   f o r   s t a l e m a t e 
                 e l s e   i f   ( m p G a m e . i n _ s t a l e m a t e ( ) )   { 
                         s t a t u s T e x t   =   ' S t a l e m a t e !   G a m e   e n d e d   i n   d r a w . ' ; 
                         d i s a b l e M p B o a r d ( ) ; 
                 } 
                 / /   C h e c k   f o r   t h r e e f o l d   r e p e t i t i o n 
                 e l s e   i f   ( m p G a m e . i n _ t h r e e f o l d _ r e p e t i t i o n ( ) )   { 
                         s t a t u s T e x t   =   ' D r a w   b y   t h r e e f o l d   r e p e t i t i o n ! ' ; 
                         d i s a b l e M p B o a r d ( ) ; 
                 } 
                 / /   C h e c k   f o r   i n s u f f i c i e n t   m a t e r i a l 
                 e l s e   i f   ( m p G a m e . i n s u f f i c i e n t _ m a t e r i a l ( ) )   { 
                         s t a t u s T e x t   =   ' D r a w   b y   i n s u f f i c i e n t   m a t e r i a l ! ' ; 
                         d i s a b l e M p B o a r d ( ) ; 
                 } 
                 / /   C h e c k   f o r   c h e c k 
                 e l s e   i f   ( m p G a m e . i n _ c h e c k ( ) )   { 
                         c o n s t   i n C h e c k   =   c u r r e n t T u r n   = = =   ' w '   ?   ' W h i t e '   :   ' B l a c k ' ; 
                         i f   ( i s P l a y e r s T u r n )   { 
                                 s t a t u s T e x t   =   ` $ { i n C h e c k }   i s   i n   c h e c k !   Y o u r   m o v e . ` ; 
                         }   e l s e   { 
                                 s t a t u s T e x t   =   ` $ { i n C h e c k }   i s   i n   c h e c k !   W a i t i n g   f o r   o p p o n e n t . ` ; 
                         } 
                 } 
                 / /   N o r m a l   g a m e   s t a t u s 
                 e l s e   { 
                         i f   ( i s P l a y e r s T u r n )   { 
                                 c o n s t   p l a y e r C o l o r   =   m p P l a y e r S i d e   = = =   ' w '   ?   ' W h i t e '   :   ' B l a c k ' ; 
                                 s t a t u s T e x t   =   ` Y o u r   t u r n   ( $ { p l a y e r C o l o r } ) ` ; 
                         }   e l s e   { 
                                 c o n s t   o p p o n e n t C o l o r   =   m p P l a y e r S i d e   = = =   ' w '   ?   ' B l a c k '   :   ' W h i t e ' ; 
                                 s t a t u s T e x t   =   ` W a i t i n g   f o r   o p p o n e n t   ( $ { o p p o n e n t C o l o r } )   t o   m o v e ` ; 
                         } 
                 } 
                 
                 / /   U p d a t e   t h e   s t a t u s   m e s s a g e 
                 m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   s t a t u s T e x t ; 
         } 
         
         f u n c t i o n   d i s a b l e M p B o a r d ( )   { 
                 m p B o a r d . d r a g g a b l e   =   f a l s e ; 
         } 
         
         f u n c t i o n   e x i t M u l t i p l a y e r G a m e ( )   { 
                 / /   C l o s e   t h e   c o n n e c t i o n 
                 i f   ( c o n n e c t i o n )   { 
                         c o n n e c t i o n . c l o s e ( ) ; 
                 } 
                 
                 / /   C l o s e   t h e   p e e r 
                 i f   ( p e e r )   { 
                         p e e r . d e s t r o y ( ) ; 
                         p e e r   =   n u l l ; 
                 } 
                 
                 / /   R e s e t   g a m e   s t a t e 
                 m p G a m e   =   n e w   C h e s s ( ) ; 
                 c o n n e c t i o n   =   n u l l ; 
                 i s H o s t   =   f a l s e ; 
                 m p P l a y e r S i d e   =   ' w ' ; 
                 m p C a p t u r e d P i e c e s   =   {   w :   [ ] ,   b :   [ ]   } ; 
                 
                 / /   R e s e t   U I 
                 m u l t i p l a y e r G a m e . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 c o n n e c t i o n S e t u p . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 c r e a t e G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 j o i n G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 
                 / /   R e t u r n   t o   m o d e   s e l e c t o r 
                 m u l t i p l a y e r M o d e . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 m o d e S e l e c t o r . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
         } 
 
         / /   C o n n e c t   t o   a   p e e r   a s   a   c l i e n t 
         f u n c t i o n   c o n n e c t T o P e e r ( p e e r I d )   { 
                 t r y   { 
                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' C o n n e c t i n g . . . ' ; 
                         
                         / /   C l e a n   t h e   p e e r   I D   ( r e m o v e   s p a c e s ,   c o n v e r t   t o   u p p e r c a s e ) 
                         c o n s t   c l e a n P e e r I d   =   p e e r I d . t r i m ( ) . t o U p p e r C a s e ( ) ; 
                         
                         i f   ( ! c l e a n P e e r I d )   { 
                                 j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' P l e a s e   e n t e r   a   v a l i d   g a m e   c o d e ' ; 
                                 r e t u r n ; 
                         } 
                         
                         c o n s o l e . l o g ( " A t t e m p t i n g   t o   c o n n e c t   t o   p e e r : " ,   c l e a n P e e r I d ) ; 
                         
                         / /   A d d   a   t i m e o u t   f o r   c o n n e c t i o n   a t t e m p t s 
                         l e t   c o n n e c t i o n T i m e o u t   =   s e t T i m e o u t ( ( )   = >   { 
                                 i f   ( c o n n e c t i o n   & &   ! c o n n e c t i o n . o p e n )   { 
                                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' C o n n e c t i o n   t i m e d   o u t .   H o s t   m a y   n o t   b e   a v a i l a b l e . ' ; 
                                         c o n s o l e . e r r o r ( " C o n n e c t i o n   a t t e m p t   t i m e d   o u t " ) ; 
                                         / /   C l e a n   u p   t h e   f a i l e d   c o n n e c t i o n 
                                         i f   ( c o n n e c t i o n )   { 
                                                 c o n n e c t i o n . c l o s e ( ) ; 
                                         } 
                                 } 
                         } ,   1 0 0 0 0 ) ;   / /   1 0   s e c o n d   t i m e o u t 
                         
                         c o n n e c t i o n   =   p e e r . c o n n e c t ( c l e a n P e e r I d ,   { 
                                 r e l i a b l e :   t r u e , 
                                 s e r i a l i z a t i o n :   ' j s o n ' 
                         } ) ; 
                         
                         c o n n e c t i o n . o n ( ' o p e n ' ,   ( )   = >   { 
                                 c l e a r T i m e o u t ( c o n n e c t i o n T i m e o u t ) ; 
                                 c o n s o l e . l o g ( " C o n n e c t e d   t o   p e e r : " ,   c o n n e c t i o n . p e e r ) ; 
                                 j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' C o n n e c t e d !   G a m e   s t a r t i n g . . . ' ; 
                                 s e t u p C o n n e c t i o n H a n d l e r s ( ) ; 
                                 
                                 / /   S w i t c h   t o   g a m e   v i e w   a f t e r   s u c c e s s f u l   c o n n e c t i o n 
                                 s e t T i m e o u t ( ( )   = >   { 
                                         j o i n G a m e S e c t i o n . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                                         c o n n e c t i o n S e t u p . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                                         m u l t i p l a y e r G a m e . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                                         
                                         / /   I n i t i a l i z e   m u l t i p l a y e r   g a m e   a s   b l a c k 
                                         m p P l a y e r S i d e   =   ' b ' ; 
                                         i n i t i a l i z e M u l t i p l a y e r G a m e ( ) ; 
                                 } ,   1 0 0 0 ) ; 
                         } ) ; 
                         
                         c o n n e c t i o n . o n ( ' e r r o r ' ,   ( e r r )   = >   { 
                                 c l e a r T i m e o u t ( c o n n e c t i o n T i m e o u t ) ; 
                                 c o n s o l e . e r r o r ( " C o n n e c t i o n   e r r o r : " ,   e r r ) ; 
                                 j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' C o n n e c t i o n   e r r o r :   '   +   ( e r r . m e s s a g e   | |   ' F a i l e d   t o   c o n n e c t ' ) ; 
                         } ) ; 
                         
                         / /   H a n d l e   c a s e   w h e r e   t h e   c o n n e c t i o n   d o e s   n o t   o p e n 
                         c o n n e c t i o n . o n ( ' c l o s e ' ,   ( )   = >   { 
                                 c l e a r T i m e o u t ( c o n n e c t i o n T i m e o u t ) ; 
                                 i f   ( ! m u l t i p l a y e r G a m e . s t y l e . d i s p l a y   | |   m u l t i p l a y e r G a m e . s t y l e . d i s p l a y   = = =   ' n o n e ' )   { 
                                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' C o n n e c t i o n   c l o s e d   b e f o r e   g a m e   c o u l d   s t a r t ' ; 
                                 } 
                         } ) ; 
                 }   c a t c h   ( e )   { 
                         c o n s o l e . e r r o r ( " E r r o r   c o n n e c t i n g   t o   p e e r : " ,   e ) ; 
                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' C o n n e c t i o n   f a i l e d :   '   +   e . m e s s a g e ; 
                 } 
         } 
 
         / /   I n i t i a l i z e   m u l t i p l a y e r   g a m e 
         f u n c t i o n   i n i t i a l i z e M u l t i p l a y e r G a m e ( )   { 
                 / /   C r e a t e   a   n e w   C h e s s . j s   i n s t a n c e 
                 m p G a m e   =   n e w   C h e s s ( ) ; 
                 
                 / /   I n i t i a l i z e   c h e s s b o a r d 
                 c o n s t   m p B o a r d C o n f i g   =   { 
                         p o s i t i o n :   ' s t a r t ' , 
                         p i e c e T h e m e :   p i e c e T h e m e , 
                         d r a g g a b l e :   ! i s M o b i l e ,     / /   D i s a b l e   d r a g g i n g   o n   m o b i l e 
                         o r i e n t a t i o n :   m p P l a y e r S i d e   = = =   ' w '   ?   ' w h i t e '   :   ' b l a c k ' , 
                         o n D r a g S t a r t :   o n M p D r a g S t a r t , 
                         o n D r o p :   o n M p D r o p , 
                         o n S n a p E n d :   o n M p S n a p E n d , 
                         s p a r e P i e c e s :   f a l s e , 
                         s h o w N o t a t i o n :   f a l s e , 
                         s n a p b a c k S p e e d :   2 5 0 , 
                         s n a p S p e e d :   1 0 0 , 
                         w i d t h :   c a l c u l a t e B o a r d S i z e ( )   / /   U s e   c a l c u l a t e d   r e s p o n s i v e   s i z e 
                 } ; 
                 
                 / /   I f   b o a r d   a l r e a d y   e x i s t s ,   d e s t r o y   i t   f i r s t 
                 i f   ( m p B o a r d )   { 
                         m p B o a r d . d e s t r o y ( ) ; 
                 } 
                 
                 $ m p B o a r d E l   =   $ ( ' # m p - g a m e - b o a r d ' ) ; 
                 m p B o a r d   =   C h e s s b o a r d ( ' m p - g a m e - b o a r d ' ,   m p B o a r d C o n f i g ) ; 
                 
                 / /   S e t u p   t h e   b o a r d   f o r   m o b i l e   c l i c k s 
                 s e t u p M o b i l e T a p H a n d l i n g ( ) ; 
                 
                 / /   P r e v e n t   s c r o l l i n g   o n   t o u c h   d e v i c e s 
                 p r e v e n t S c r o l l O n C h e s s b o a r d ( ) ; 
                 
                 / /   S e t   t h e   p l a y e r   c o l o r   i n f o 
                 m p P l a y e r C o l o r . t e x t C o n t e n t   =   m p P l a y e r S i d e   = = =   ' w '   ?   ' W h i t e '   :   ' B l a c k ' ; 
                 m p P l a y e r C o l o r . c l a s s N a m e   =   m p P l a y e r S i d e   = = =   ' w '   ?   ' w h i t e - t e x t '   :   ' b l a c k - t e x t ' ; 
                 
                 / /   I n i t i a l i z e   m o v e   h i s t o r y 
                 c l e a r M p M o v e H i s t o r y ( ) ; 
                 
                 / /   U p d a t e   c a p t u r e d   p i e c e s 
                 m p C a p t u r e d P i e c e s   =   {   w :   [ ] ,   b :   [ ]   } ; 
                 u p d a t e M p C a p t u r e d P i e c e s ( ) ; 
                 
                 / /   U p d a t e   s t a t u s 
                 u p d a t e M p G a m e S t a t u s ( ) ; 
                 
                 / /   R e s i z e   h a n d l e r 
                 $ ( w i n d o w ) . r e s i z e ( ( )   = >   { 
                         i f   ( m p B o a r d )   { 
                                 m p B o a r d . r e s i z e ( c a l c u l a t e B o a r d S i z e ( ) ) ; 
                         } 
                 } ) ; 
                 
                 / /   F o r c e   a   r e s i z e   a f t e r   c r e a t i o n   t o   e n s u r e   p r o p e r   d i s p l a y 
                 s e t T i m e o u t ( ( )   = >   { 
                         i f   ( m p B o a r d )   { 
                                 m p B o a r d . r e s i z e ( c a l c u l a t e B o a r d S i z e ( ) ) ; 
                         } 
                 } ,   1 0 0 ) ; 
                 
                 / /   S e t   u p   c o n n e c t i o n   d a t a   h a n d l e r s   i f   n o t   a l r e a d y   s e t 
                 i f   ( c o n n e c t i o n   & &   ! c o n n e c t i o n . l i s t e n e r C o u n t ( ' d a t a ' ) )   { 
                         s e t u p C o n n e c t i o n H a n d l e r s ( ) ; 
                 } 
         } 
 
         / /   E v e n t   h a n d l e r   f o r   r e s i g n   b u t t o n   c l i c k 
         r e s i g n B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 i f   ( ! c o n n e c t i o n   | |   ! c o n n e c t i o n . o p e n )   { 
                         m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' N o   c o n n e c t i o n   t o   o p p o n e n t ' ; 
                         r e t u r n ; 
                 } 
                 
                 i f   ( c o n f i r m ( ' A r e   y o u   s u r e   y o u   w a n t   t o   r e s i g n ? ' ) )   { 
                         c o n s o l e . l o g ( ' P l a y e r   r e s i g n i n g ' ) ; 
                         
                         / /   S e n d   r e s i g n   m e s s a g e   t o   o p p o n e n t 
                         s e n d G a m e D a t a ( { 
                                 t y p e :   ' r e s i g n ' 
                         } ) ; 
                         
                         / /   U p d a t e   s t a t u s   a n d   d i s a b l e   b o a r d 
                         m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' Y o u   r e s i g n e d .   G a m e   o v e r . ' ; 
                         d i s a b l e M p B o a r d ( ) ; 
                 } 
         } ) ; 
 
         / /   E v e n t   h a n d l e r   f o r   b a c k   b u t t o n   f r o m   m u l t i p l a y e r   g a m e 
         e x i t M p B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 / /   C o n f i r m   l e a v i n g   g a m e   i f   i t ' s   i n   p r o g r e s s 
                 i f   ( c o n n e c t i o n   & &   c o n n e c t i o n . o p e n   & &   ! m p G a m e . g a m e _ o v e r ( ) )   { 
                         i f   ( ! c o n f i r m ( ' A r e   y o u   s u r e   y o u   w a n t   t o   l e a v e   t h e   g a m e ?   T h i s   w i l l   c o u n t   a s   a   r e s i g n a t i o n . ' ) )   { 
                                 r e t u r n ; 
                         } 
                         
                         / /   N o t i f y   o p p o n e n t   o f   r e s i g n a t i o n   i f   c o n n e c t e d 
                         s e n d G a m e D a t a ( { 
                                 t y p e :   ' r e s i g n ' 
                         } ) ; 
                 } 
                 
                 / /   C l e a n   u p   c o n n e c t i o n   a n d   r e s e t   U I 
                 c l e a n u p M p C o n n e c t i o n ( ) ; 
                 r e s e t M p U I ( ) ; 
         } ) ; 
 
         / /   C l e a n   u p   t h e   m u l t i p l a y e r   c o n n e c t i o n 
         f u n c t i o n   c l e a n u p M p C o n n e c t i o n ( )   { 
                 c o n s o l e . l o g ( ' C l e a n i n g   u p   m u l t i p l a y e r   c o n n e c t i o n ' ) ; 
                 
                 i f   ( c o n n e c t i o n )   { 
                         c o n n e c t i o n . c l o s e ( ) ; 
                         c o n n e c t i o n   =   n u l l ; 
                 } 
                 
                 i f   ( p e e r )   { 
                         p e e r . d e s t r o y ( ) ; 
                         p e e r   =   n u l l ; 
                 } 
         } 
 
         / /   R e s e t   t h e   m u l t i p l a y e r   U I 
         f u n c t i o n   r e s e t M p U I ( )   { 
                 m u l t i p l a y e r G a m e . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 c o n n e c t i o n S e t u p . s t y l e . d i s p l a y   =   ' n o n e ' ; 
                 m o d e S e l e c t o r . s t y l e . d i s p l a y   =   ' b l o c k ' ; 
                 
                 / /   C l e a r   f o r m   f i e l d s 
                 g a m e C o d e D i s p l a y . v a l u e   =   ' ' ; 
                 g a m e C o d e I n p u t . v a l u e   =   ' ' ; 
                 
                 / /   R e s e t   s t a t u s   m e s s a g e s 
                 s t a t u s T e x t . t e x t C o n t e n t   =   ' ' ; 
                 j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' ' ; 
                 m p S t a t u s M e s s a g e . t e x t C o n t e n t   =   ' ' ; 
                 
                 / /   C l e a r   g a m e   e l e m e n t s 
                 r e s e t M p C a p t u r e d P i e c e s ( ) ; 
                 c l e a r M p M o v e H i s t o r y ( ) ; 
                 
                 / /   R e s e t   b o a r d   i f   i t   e x i s t s 
                 i f   ( m p B o a r d )   { 
                         m p B o a r d . p o s i t i o n ( ' s t a r t ' ) ; 
                 } 
         } 
 
         / /   H a n d l e   t h e   j o i n   g a m e   b u t t o n   c l i c k 
         c o n n e c t B t n . a d d E v e n t L i s t e n e r ( ' c l i c k ' ,   ( )   = >   { 
                 c o n s t   g a m e C o d e   =   g a m e C o d e I n p u t . v a l u e . t r i m ( ) . t o U p p e r C a s e ( ) ; 
                 
                 i f   ( ! g a m e C o d e )   { 
                         j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' P l e a s e   e n t e r   a   v a l i d   g a m e   c o d e ' ; 
                         r e t u r n ; 
                 } 
                 
                 j o i n S t a t u s T e x t . t e x t C o n t e n t   =   ' C o n n e c t i n g   t o   g a m e . . . ' ; 
                 c o n n e c t B t n . d i s a b l e d   =   t r u e ; 
                 
                 c o n n e c t T o P e e r ( g a m e C o d e ) ; 
         } ) ; 
 
         / /   A d d   o r   u p d a t e   r e s i z e   h a n d l e r   t o   e n s u r e   p r o p e r   m o b i l e   d i s p l a y 
         $ ( w i n d o w ) . r e s i z e ( f u n c t i o n ( )   { 
                 i f   ( b o a r d )   { 
                         / /   U s e   o u r   r e s p o n s i v e   b o a r d   s i z e   c a l c u l a t i o n 
                         b o a r d . r e s i z e ( c a l c u l a t e B o a r d S i z e ( ) ) ; 
                 } 
                 
                 i f   ( m p B o a r d )   { 
                         / /   U s e   o u r   r e s p o n s i v e   b o a r d   s i z e   c a l c u l a t i o n 
                         m p B o a r d . r e s i z e ( c a l c u l a t e B o a r d S i z e ( ) ) ; 
                 } 
         } ) ; 
 
         / /   A d d   g l o b a l   t o u c h   e v e n t   h a n d l i n g   t o   p r e v e n t   s c r o l l i n g   d u r i n g   p i e c e   d r a g 
         d o c u m e n t . a d d E v e n t L i s t e n e r ( ' D O M C o n t e n t L o a d e d ' ,   f u n c t i o n ( )   { 
                 c o n s o l e . l o g ( " A d d i n g   g l o b a l   t o u c h   h a n d l e r s " ) ; 
                 / /   A p p l y   t o u c h   e v e n t   b l o c k i n g   t o   c h e s s   e l e m e n t s 
                 d o c u m e n t . a d d E v e n t L i s t e n e r ( ' t o u c h m o v e ' ,   f u n c t i o n ( e )   { 
                         / /   C h e c k   i f   t h e   t o u c h   i s   o n   a   c h e s s   b o a r d ,   p i e c e ,   o r   s q u a r e 
                         c o n s t   t a r g e t   =   e . t a r g e t ; 
                         i f   ( t a r g e t . t a g N a m e   = = =   ' I M G '   & &   t a r g e t . c l a s s L i s t . c o n t a i n s ( ' p i e c e - 4 1 7 d b ' )   | | 
                                 ( t a r g e t . c l o s e s t   & &   ( t a r g e t . c l o s e s t ( ' # g a m e - b o a r d ' )   | |   t a r g e t . c l o s e s t ( ' # m p - g a m e - b o a r d ' ) ) ) )   { 
                                 c o n s o l e . l o g ( " P r e v e n t e d   s c r o l l   o n   c h e s s   e l e m e n t " ) ; 
                                 e . p r e v e n t D e f a u l t ( ) ; 
                         } 
                 } ,   {   p a s s i v e :   f a l s e   } ) ; 
         } ) ; 
 
         / /   H a n d l e   c l i c k   f o r   m o b i l e   t a p   i n t e r f a c e   ( s i n g l e   p l a y e r ) 
         f u n c t i o n   h a n d l e B o a r d C l i c k ( s q u a r e )   { 
                 c o n s o l e . l o g ( ' h a n d l e B o a r d C l i c k   c a l l e d : ' ,   s q u a r e ,   ' i s M o b i l e : ' ,   i s M o b i l e ,   ' i s P l a y e r T u r n : ' ,   i s P l a y e r T u r n ) ; 
                 
                 i f   ( ! i s M o b i l e   | |   ! i s P l a y e r T u r n )   r e t u r n ; 
                 
                 / /   G e t   c l i c k e d   p i e c e   i f   a n y 
                 c o n s t   p o s i t i o n   =   b o a r d . p o s i t i o n ( ) ; 
                 c o n s t   p i e c e   =   p o s i t i o n [ s q u a r e ]   | |   n u l l ; 
                 c o n s o l e . l o g ( ' C l i c k e d   o n   s q u a r e : ' ,   s q u a r e ,   ' P i e c e : ' ,   p i e c e ) ; 
                 
                 / /   I f   n o   p i e c e   s e l e c t e d   y e t   a n d   a   v a l i d   p i e c e   i s   c l i c k e d 
                 i f   ( ! s e l e c t e d S q u a r e   & &   p i e c e )   { 
                         c o n s t   p i e c e C o l o r   =   p i e c e . c h a r A t ( 0 ) ; 
                         
                         / /   C h e c k   i f   i t ' s   t h e   p l a y e r ' s   p i e c e 
                         i f   ( ( p i e c e C o l o r   = = =   ' w '   & &   p l a y e r C o l o r   = = =   ' w ' )   | |   
                                 ( p i e c e C o l o r   = = =   ' b '   & &   p l a y e r C o l o r   = = =   ' b ' ) )   { 
                                 
                                 s e l e c t e d S q u a r e   =   s q u a r e ; 
                                 s e l e c t e d P i e c e   =   p i e c e ; 
                                 c o n s o l e . l o g ( ' S e l e c t e d   p i e c e : ' ,   p i e c e ,   ' a t   s q u a r e : ' ,   s q u a r e ) ; 
                                 
                                 / /   H i g h l i g h t   s e l e c t e d   s q u a r e 
                                 r e m o v e H i g h l i g h t i n g ( ) ; 
                                 h i g h l i g h t S q u a r e ( s q u a r e ) ; 
                                 
                                 / /   H i g h l i g h t   v a l i d   m o v e s 
                                 c o n s t   m o v e s   =   g a m e . m o v e s ( { 
                                         s q u a r e :   s q u a r e , 
                                         v e r b o s e :   t r u e 
                                 } ) ; 
                                 
                                 c o n s o l e . l o g ( ' V a l i d   m o v e s : ' ,   m o v e s . l e n g t h ) ; 
                                 
                                 f o r   ( l e t   m o v e   o f   m o v e s )   { 
                                         $ b o a r d E l . f i n d ( ` . s q u a r e - $ { m o v e . t o } ` ) . a d d C l a s s ( ' h i g h l i g h t - t a r g e t ' ) ; 
                                 } 
                                 
                                 r e t u r n ; 
                         } 
                 } 
                 
                 / /   I f   a   p i e c e   i s   a l r e a d y   s e l e c t e d   a n d   a n o t h e r   s q u a r e   i s   c l i c k e d 
                 i f   ( s e l e c t e d S q u a r e )   { 
                         c o n s o l e . l o g ( ' A t t e m p t i n g   m o v e   f r o m ' ,   s e l e c t e d S q u a r e ,   ' t o ' ,   s q u a r e ) ; 
                         
                         / /   T r y   t o   m a k e   t h e   m o v e 
                         c o n s t   m o v e   =   m a k e M o v e ( s e l e c t e d S q u a r e ,   s q u a r e ) ; 
                         
                         i f   ( m o v e )   { 
                                 c o n s o l e . l o g ( ' M o v e   s u c c e s s f u l ' ) ; 
                                 / /   R e s e t   s e l e c t i o n   a f t e r   s u c c e s s f u l   m o v e 
                                 s e l e c t e d S q u a r e   =   n u l l ; 
                                 s e l e c t e d P i e c e   =   n u l l ; 
                                 r e m o v e H i g h l i g h t i n g ( ) ; 
                         }   e l s e   { 
                                 c o n s o l e . l o g ( ' M o v e   f a i l e d   o r   s a m e   s q u a r e   c l i c k e d   a g a i n ' ) ; 
                                 / /   I f   i l l e g a l   m o v e   o r   c l i c k i n g   t h e   s a m e   p i e c e   a g a i n ,   r e s e t   s e l e c t i o n 
                                 s e l e c t e d S q u a r e   =   n u l l ; 
                                 s e l e c t e d P i e c e   =   n u l l ; 
                                 r e m o v e H i g h l i g h t i n g ( ) ; 
                                 
                                 / /   I f   c l i c k e d   o n   a n o t h e r   v a l i d   p i e c e ,   s e l e c t   t h a t   i n s t e a d 
                                 s e t T i m e o u t ( ( )   = >   h a n d l e B o a r d C l i c k ( s q u a r e ) ,   5 0 ) ; 
                         } 
                 } 
         } 
 
         / /   F u n c t i o n   t o   m a k e   a   m o v e   a n d   h a n d l e   t h e   r e s u l t 
         f u n c t i o n   m a k e M o v e ( s o u r c e ,   t a r g e t )   { 
                 / /   C h e c k   i f   m o v e   i s   l e g a l 
                 c o n s t   m o v e   =   g a m e . m o v e ( { 
                         f r o m :   s o u r c e , 
                         t o :   t a r g e t , 
                         p r o m o t i o n :   ' q '   / /   A u t o   p r o m o t e   t o   q u e e n   f o r   s i m p l i c i t y 
                 } ) ; 
                 
                 i f   ( ! m o v e )   r e t u r n   n u l l ;   / /   I l l e g a l   m o v e 
                 
                 / /   U p d a t e   b o a r d 
                 b o a r d . p o s i t i o n ( g a m e . f e n ( ) ) ; 
                 
                 / /   C h e c k   f o r   c a p t u r e s 
                 c h e c k F o r C a p t u r e ( m o v e ) ; 
                 
                 / /   H i g h l i g h t   t h e   m o v e 
                 r e m o v e H i g h l i g h t i n g ( ) ; 
                 h i g h l i g h t L a s t M o v e ( s o u r c e ,   t a r g e t ) ; 
                 l a s t M o v e S o u r c e   =   s o u r c e ; 
                 l a s t M o v e T a r g e t   =   t a r g e t ; 
                 
                 / /   U p d a t e   m o v e   h i s t o r y   a n d   g a m e   s t a t u s 
                 a d d M o v e T o H i s t o r y ( m o v e ) ; 
                 u p d a t e M o v e L i s t ( ) ; 
                 u p d a t e G a m e S t a t u s ( ) ; 
                 
                 / /   U p d a t e   t u r n   i n d i c a t o r   a n d   l e t   A I   m a k e   i t s   m o v e 
                 u p d a t e T u r n I n d i c a t o r ( ) ; 
                 i s P l a y e r T u r n   =   f a l s e ; 
                 s e t T i m e o u t ( m a k e A I M o v e ,   5 0 0 ) ; 
                 
                 r e t u r n   m o v e ; 
         } 
 
         / /   N e w   u t i l i t y   f u n c t i o n   t o   h i g h l i g h t   v a l i d   t a r g e t   s q u a r e s 
         f u n c t i o n   h i g h l i g h t V a l i d M o v e s ( s q u a r e )   { 
                 c o n s t   m o v e s   =   g a m e . m o v e s ( { 
                         s q u a r e :   s q u a r e , 
                         v e r b o s e :   t r u e 
                 } ) ; 
                 
                 f o r   ( l e t   m o v e   o f   m o v e s )   { 
                         $ b o a r d E l . f i n d ( ` . s q u a r e - $ { m o v e . t o } ` ) . a d d C l a s s ( ' h i g h l i g h t - t a r g e t ' ) ; 
                 } 
         } 
 
         / /   H a n d l e   c l i c k   f o r   m o b i l e   t a p   i n t e r f a c e   ( m u l t i p l a y e r ) 
         f u n c t i o n   h a n d l e M p B o a r d C l i c k ( s q u a r e )   { 
                 c o n s o l e . l o g ( ' h a n d l e M p B o a r d C l i c k   c a l l e d : ' ,   s q u a r e ,   ' i s M o b i l e : ' ,   i s M o b i l e ) ; 
                 
                 i f   ( ! i s M o b i l e )   r e t u r n ; 
                 
                 / /   O n l y   a l l o w   i n t e r a c t i o n   i f   i t ' s   t h e   p l a y e r ' s   t u r n 
                 c o n s t   i s P l a y e r T u r n   =   ( m p P l a y e r S i d e   = = =   ' w '   & &   m p G a m e . t u r n ( )   = = =   ' w ' )   | |   
                                                             ( m p P l a y e r S i d e   = = =   ' b '   & &   m p G a m e . t u r n ( )   = = =   ' b ' ) ; 
                 
                 c o n s o l e . l o g ( ' I s   p l a y e r   t u r n : ' ,   i s P l a y e r T u r n ,   ' P l a y e r   s i d e : ' ,   m p P l a y e r S i d e ,   ' G a m e   t u r n : ' ,   m p G a m e . t u r n ( ) ) ; 
                 
                 i f   ( ! i s P l a y e r T u r n )   r e t u r n ; 
                 
                 / /   G e t   c l i c k e d   p i e c e   i f   a n y 
                 c o n s t   p o s i t i o n   =   m p B o a r d . p o s i t i o n ( ) ; 
                 c o n s t   p i e c e   =   p o s i t i o n [ s q u a r e ]   | |   n u l l ; 
                 c o n s o l e . l o g ( ' M P   c l i c k e d   o n   s q u a r e : ' ,   s q u a r e ,   ' P i e c e : ' ,   p i e c e ) ; 
                 
                 / /   I f   n o   p i e c e   s e l e c t e d   y e t   a n d   a   v a l i d   p i e c e   i s   c l i c k e d 
                 i f   ( ! s e l e c t e d S q u a r e   & &   p i e c e )   { 
                         c o n s t   p i e c e C o l o r   =   p i e c e . c h a r A t ( 0 ) ; 
                         
                         / /   C h e c k   i f   i t ' s   t h e   p l a y e r ' s   p i e c e 
                         i f   ( ( p i e c e C o l o r   = = =   ' w '   & &   m p P l a y e r S i d e   = = =   ' w ' )   | |   
                                 ( p i e c e C o l o r   = = =   ' b '   & &   m p P l a y e r S i d e   = = =   ' b ' ) )   { 
                                 
                                 s e l e c t e d S q u a r e   =   s q u a r e ; 
                                 s e l e c t e d P i e c e   =   p i e c e ; 
                                 c o n s o l e . l o g ( ' M P   S e l e c t e d   p i e c e : ' ,   p i e c e ,   ' a t   s q u a r e : ' ,   s q u a r e ) ; 
                                 
                                 / /   H i g h l i g h t   s e l e c t e d   s q u a r e 
                                 r e m o v e M p H i g h l i g h t i n g ( ) ; 
                                 h i g h l i g h t M p S q u a r e ( s q u a r e ) ; 
                                 
                                 / /   H i g h l i g h t   v a l i d   m o v e s 
                                 c o n s t   m o v e s   =   m p G a m e . m o v e s ( { 
                                         s q u a r e :   s q u a r e , 
                                         v e r b o s e :   t r u e 
                                 } ) ; 
                                 
                                 c o n s o l e . l o g ( ' M P   V a l i d   m o v e s : ' ,   m o v e s . l e n g t h ) ; 
                                 
                                 f o r   ( l e t   m o v e   o f   m o v e s )   { 
                                         $ m p B o a r d E l . f i n d ( ` . s q u a r e - $ { m o v e . t o } ` ) . a d d C l a s s ( ' h i g h l i g h t - t a r g e t ' ) ; 
                                 } 
                                 
                                 r e t u r n ; 
                         } 
                 } 
                 
                 / /   I f   a   p i e c e   i s   a l r e a d y   s e l e c t e d   a n d   a n o t h e r   s q u a r e   i s   c l i c k e d 
                 i f   ( s e l e c t e d S q u a r e )   { 
                         c o n s o l e . l o g ( ' M P   A t t e m p t i n g   m o v e   f r o m ' ,   s e l e c t e d S q u a r e ,   ' t o ' ,   s q u a r e ) ; 
                         
                         / /   T r y   t o   m a k e   t h e   m o v e 
                         c o n s t   m o v e R e s u l t   =   m a k e M p M o v e ( s e l e c t e d S q u a r e ,   s q u a r e ) ; 
                         
                         i f   ( m o v e R e s u l t   & &   m o v e R e s u l t . m o v e )   { 
                                 c o n s o l e . l o g ( ' M P   M o v e   s u c c e s s f u l ' ) ; 
                                 / /   R e s e t   s e l e c t i o n   a f t e r   s u c c e s s f u l   m o v e 
                                 s e l e c t e d S q u a r e   =   n u l l ; 
                                 s e l e c t e d P i e c e   =   n u l l ; 
                                 r e m o v e M p H i g h l i g h t i n g ( ) ; 
                                 
                                 / /   S e n d   m o v e   t o   o p p o n e n t 
                                 s e n d G a m e D a t a ( { 
                                         t y p e :   ' m o v e ' , 
                                         f r o m :   m o v e R e s u l t . m o v e . f r o m , 
                                         t o :   m o v e R e s u l t . m o v e . t o , 
                                         p r o m o t i o n :   ' q ' , 
                                         f e n :   m p G a m e . f e n ( ) 
                                 } ) ; 
                         }   e l s e   { 
                                 c o n s o l e . l o g ( ' M P   M o v e   f a i l e d   o r   s a m e   s q u a r e   c l i c k e d   a g a i n ' ) ; 
                                 / /   I f   i l l e g a l   m o v e   o r   c l i c k i n g   t h e   s a m e   p i e c e   a g a i n ,   r e s e t   s e l e c t i o n 
                                 s e l e c t e d S q u a r e   =   n u l l ; 
                                 s e l e c t e d P i e c e   =   n u l l ; 
                                 r e m o v e M p H i g h l i g h t i n g ( ) ; 
                                 
                                 / /   I f   c l i c k e d   o n   a n o t h e r   v a l i d   p i e c e ,   s e l e c t   t h a t   i n s t e a d 
                                 s e t T i m e o u t ( ( )   = >   h a n d l e M p B o a r d C l i c k ( s q u a r e ) ,   5 0 ) ; 
                         } 
                 } 
         } 
 
         / /   F u n c t i o n   t o   m a k e   a   m o v e   a n d   h a n d l e   t h e   r e s u l t   i n   m u l t i p l a y e r 
         f u n c t i o n   m a k e M p M o v e ( s o u r c e ,   t a r g e t )   { 
                 / /   C h e c k   i f   m o v e   i s   l e g a l 
                 c o n s t   m o v e   =   m p G a m e . m o v e ( { 
                         f r o m :   s o u r c e , 
                         t o :   t a r g e t , 
                         p r o m o t i o n :   ' q '   / /   A u t o   p r o m o t e   t o   q u e e n   f o r   s i m p l i c i t y 
                 } ) ; 
                 
                 i f   ( ! m o v e )   r e t u r n   {   v a l i d :   f a l s e   } ;   / /   I l l e g a l   m o v e 
                 
                 / /   U p d a t e   b o a r d 
                 m p B o a r d . p o s i t i o n ( m p G a m e . f e n ( ) ) ; 
                 
                 / /   C h e c k   f o r   c a p t u r e s 
                 c h e c k M p F o r C a p t u r e ( {   m o v e   } ) ; 
                 
                 / /   H i g h l i g h t   t h e   m o v e 
                 r e m o v e M p H i g h l i g h t i n g ( ) ; 
                 h i g h l i g h t M p L a s t M o v e ( s o u r c e ,   t a r g e t ) ; 
                 m p L a s t M o v e S o u r c e   =   s o u r c e ; 
                 m p L a s t M o v e T a r g e t   =   t a r g e t ; 
                 
                 / /   U p d a t e   m o v e   h i s t o r y   a n d   g a m e   s t a t u s 
                 a d d M p M o v e T o H i s t o r y ( m o v e ) ; 
                 u p d a t e M p G a m e S t a t u s ( ) ; 
                 
                 r e t u r n   {   v a l i d :   t r u e ,   m o v e   } ; 
         } 
 
         / /   F u n c t i o n   t o   c a l c u l a t e   r e s p o n s i v e   b o a r d   s i z e 
         f u n c t i o n   c a l c u l a t e B o a r d S i z e ( )   { 
                 l e t   w i d t h   =   w i n d o w . i n n e r W i d t h ; 
                 l e t   b o a r d S i z e   =   w i d t h ; 
                 
                 / /   F o r   d e s k t o p   o r   l a r g e r   s c r e e n s 
                 i f   ( w i d t h   > =   7 6 8 )   { 
                         b o a r d S i z e   =   6 0 0 ;   / /   M a x   b o a r d   s i z e   f o r   d e s k t o p 
                 }   
                 / /   F o r   t a b l e t s 
                 e l s e   i f   ( w i d t h   > =   4 8 0 )   { 
                         c o n s t   c o n t a i n e r W i d t h   =   d o c u m e n t . q u e r y S e l e c t o r ( ' . g a m e - b o a r d - c o n t a i n e r ' ) ? . c l i e n t W i d t h   | |   w i d t h   -   4 0 ; 
                         b o a r d S i z e   =   M a t h . m i n ( c o n t a i n e r W i d t h   -   4 0 ,   4 8 0 ) ; 
                 }   
                 / /   F o r   m o b i l e   p h o n e s 
                 e l s e   { 
                         c o n s t   c o n t a i n e r W i d t h   =   d o c u m e n t . q u e r y S e l e c t o r ( ' . g a m e - b o a r d - c o n t a i n e r ' ) ? . c l i e n t W i d t h   | |   w i d t h   -   2 0 ; 
                         b o a r d S i z e   =   M a t h . m i n ( c o n t a i n e r W i d t h   -   2 0 ,   3 2 0 ) ; 
                 } 
                 
                 r e t u r n   M a t h . f l o o r ( b o a r d S i z e ) ; 
         } 
         
         / /   U p d a t e   b o a r d   s i z e   o n   w i n d o w   r e s i z e 
         w i n d o w . a d d E v e n t L i s t e n e r ( ' r e s i z e ' ,   ( )   = >   { 
                 i f   ( b o a r d )   { 
                         b o a r d . r e s i z e ( c a l c u l a t e B o a r d S i z e ( ) ) ; 
                 } 
                 i f   ( m p B o a r d )   { 
                         m p B o a r d . r e s i z e ( c a l c u l a t e B o a r d S i z e ( ) ) ; 
                 } 
         } ) ; 
 } ) ;   