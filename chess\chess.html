<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#050A24">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <title>Chess - Two-Games</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>♞</text></svg>">
    <!-- jQuery required for chessboard.js -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Load chess.js library for chess logic -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.3/chess.min.js"></script>
    <!-- Load chessboard.js and its CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PeerJS for WebRTC -->
    <script src="https://unpkg.com/peerjs@1.4.7/dist/peerjs.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="chess.css">
    
    <!-- Mobile touch detection script -->
    <script>
        // Detect if device is a touch device
        window.isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // Prevent double-tap zoom on mobile devices
        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('#game-board') || e.target.closest('#mp-game-board')) {
                e.preventDefault();
                // Only prevent default if it's on the game board
            }
        }, {passive: false});
        
        // Prevent page scroll when interacting with the board
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('#game-board') || e.target.closest('#mp-game-board') || 
                e.target.closest('.piece-417db')) {
                e.preventDefault();
            }
        }, {passive: false});
    </script>
    
    <!-- Override styles for chessboard.js drag behavior -->
    <style>
        /* Ensure dragged pieces are always visible */
        img.piece-417db {
            opacity: 1 !important;
        }
        
        /* Fix for ghost pieces during drag */
        body > img[src*="chesspieces"] {
            opacity: 1 !important;
            transform: scale(1.15);
            filter: drop-shadow(3px 6px 8px rgba(0, 0, 0, 0.7));
            pointer-events: none;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1><a href="../../index.html">Two-Games</a></h1>
            </div>
            <ul class="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="../../games.html" class="active">Games</a></li>
                <li><a href="../../about.html">About</a></li>
            </ul>
        </nav>
    </header>

    <main class="main-container">
        <h1 class="game-title">Chess</h1>
        
        <!-- Mode Selector -->
        <div id="mode-selector" class="mode-selector">
            <h2 class="mode-title">Choose Game Mode</h2>
            <p>Play against the AI or challenge another player in real-time</p>
            <div class="mode-buttons">
                <button id="single-player-btn" class="mode-btn primary-btn">
                    <i class="fas fa-robot"></i> Single Player
                </button>
                <button id="multiplayer-btn" class="mode-btn secondary-btn">
                    <i class="fas fa-user-friends"></i> Multiplayer
                </button>
            </div>
        </div>
        
        <!-- Single Player Mode -->
        <div id="single-player-mode" class="single-player-mode">
            <div class="game-controls">
                <div class="game-settings">
                    <div class="difficulty-selector">
                        <label for="difficulty">Difficulty:</label>
                        <select id="difficulty">
                            <option value="1">Beginner</option>
                            <option value="2" selected>Intermediate</option>
                            <option value="3">Advanced</option>
                            <option value="4">Master</option>
                        </select>
                    </div>
                    
                    <div class="color-selector">
                        <span class="color-selector-title">Play as:</span>
                        <button id="play-white" class="color-btn active">White</button>
                        <button id="play-black" class="color-btn">Black</button>
                    </div>
                </div>
            </div>
            
            <div class="game-container">
                <div class="game-board-container">
                    <div id="game-board"></div>
                </div>
                
                <div class="game-info">
                    <div class="status-container">
                        <div id="status-message" class="status-message">Choose to begin</div>
                        <div class="turn-indicator">
                            <div class="turn-player active" id="player-turn">
                                <i class="fas fa-user"></i>
                                <span>Your Turn</span>
                            </div>
                            <div class="turn-player" id="ai-turn">
                                <i class="fas fa-robot"></i>
                                <span>AI Turn</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="captured-pieces-container">
                        <div class="captured-title">White Captured:</div>
                        <div id="white-captured-pieces" class="captured-pieces"></div>
                        <div class="captured-title">Black Captured:</div>
                        <div id="black-captured-pieces" class="captured-pieces"></div>
                    </div>
                    
                    <div class="move-list-container">
                        <div class="move-list-title">Move History:</div>
                        <div id="move-list" class="move-list"></div>
                    </div>
                    
                    <div class="game-actions">
                        <button id="new-game-btn" class="game-btn primary-btn">New Game</button>
                        <button id="undo-btn" class="game-btn secondary-btn">Undo Move</button>
                        <button id="exit-single-btn" class="game-btn danger-btn">Exit Game</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Multiplayer Mode -->
        <div id="multiplayerMode" class="multiplayer-mode">
            <div id="connection-setup" class="connection-setup">
                <h2 class="setup-title">Play Chess Online</h2>
                <div class="setup-buttons">
                    <button id="createBtn" class="game-btn primary-btn">Create a Game</button>
                    <button id="joinBtn" class="game-btn secondary-btn">Join a Game</button>
                </div>
                
                <div id="createGameSection" class="create-game-section">
                    <h3 class="section-title">Share this code with your opponent:</h3>
                    <div class="connection-field">
                        <div class="game-code">
                            <input id="gameCodeDisplay" class="code-input" type="text" readonly>
                            <button id="copyCodeBtn" class="game-btn primary-btn"><i class="fas fa-copy"></i> Copy Code</button>
                        </div>
                    </div>
                    <div id="statusText" class="connection-status">Generating game code...</div>
                </div>
                
                <div id="joinGameSection" class="join-game-section">
                    <h3 class="section-title">Enter the game code:</h3>
                    <div class="connection-field">
                        <div class="game-code">
                            <input id="gameCodeInput" class="code-input" type="text" placeholder="ENTER CODE" maxlength="6">
                            <button id="connectBtn" class="game-btn primary-btn">Connect</button>
                        </div>
                    </div>
                    <div id="joinStatusText" class="connection-status">Enter the code provided by your opponent.</div>
                </div>
            </div>
            
            <div id="multiplayer-game" class="multiplayer-game">
                <div class="mp-game-container">
                    <div class="mp-game-board-container">
                        <div id="mp-game-board"></div>
                    </div>
                    
                    <div class="mp-game-info">
                        <div class="mp-status-container">
                            <div id="mp-status" class="mp-status-message">Game ready. White to move.</div>
                            <div id="mp-player-info" class="mp-player-info">
                                <span class="mp-player-info-label">You are playing as:</span>
                                <span id="mp-player-color" class="mp-player-color">White</span>
                            </div>
                        </div>
                        
                        <div class="captured-pieces-container">
                            <div class="captured-title">White Captured:</div>
                            <div id="mp-white-captured" class="captured-pieces"></div>
                            <div class="captured-title">Black Captured:</div>
                            <div id="mp-black-captured" class="captured-pieces"></div>
                        </div>
                        
                        <div class="move-list-container">
                            <div class="move-list-title">Move History:</div>
                            <div id="mp-move-list" class="move-list"></div>
                        </div>
                        
                        <div class="game-actions">
                            <button id="resign-btn" class="game-btn danger-btn">Resign</button>
                            <button id="exit-mp-btn" class="game-btn secondary-btn">Exit Game</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <h2>Two-Games</h2>
                <p>Connect and play without barriers</p>
            </div>
            <div class="footer-links">
                <div class="link-group">
                    <h3>Games</h3>
                    <ul>
                        <li><a href="../tictactoe/tictactoe.html">Tic Tac Toe</a></li>
                        <li><a href="chess.html" class="active">Chess</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h3>About</h3>
                    <ul>
                        <li><a href="../../about.html">About Us</a></li>
                        <li><a href="../../about.html#how-it-works">How It Works</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Two-Games. All rights reserved.</p>
        </div>
    </footer>

    <!-- Custom Scripts -->
    <script src="engine.js"></script>
    <script src="chess.js"></script>
</body>
</html> 