/* Chess Game Styles */

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f8fa;
}

.main-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: calc(100vh - 320px);
}

/* Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    width: 100%;
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #3498db;
}

.main-title-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 20px 0;
}

.main-title-header .back-btn {
    position: absolute;
    left: 0;
}

.game-title {
    font-size: 2.5rem;
    text-align: center;
    margin: 0;
    color: #2c3e50;
    flex: 1;
}

/* Chess piece decorative styling with spacing and sizing */
.game-title {
    font-size: 2.5rem;
    text-align: center;
    margin: 0;
    color: #2c3e50;
    flex: 1;
    font-variant-emoji: text;
}

.mode-title,
.lobby-title,
.section-title {
    position: relative;
    font-variant-emoji: text;
}

/* Enhanced chess piece styling - size only */
.chess-piece-large {
    font-size: 1.4em;
    display: inline-block;
    vertical-align: middle;
    margin: 0 15px;
}

.chess-piece-medium {
    font-size: 1.2em;
    display: inline-block;
    vertical-align: middle;
    margin: 0 8px;
}

/* Mode Selector */
.mode-selector {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    margin: 0 auto 30px;
}

.mode-title {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.8rem;
}

.mode-selector p {
    margin-bottom: 20px;
    color: #666;
}

.mode-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: center;
    align-items: center;
}

.button-row {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.mode-btn {
    padding: 12px 25px;
    font-size: 1.1rem;
    min-width: 180px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.mode-btn.primary-btn {
    background-color: #5865f2;
    color: white;
}

.mode-btn.primary-btn:hover {
    background-color: #4752c4;
    transform: translateY(-2px);
}

.mode-btn.secondary-btn {
    background-color: #ecf0f1;
    color: #2c3e50;
}

.mode-btn.secondary-btn:hover {
    background-color: #d6e0e4;
    transform: translateY(-2px);
}

.mode-btn.disabled-btn {
    background-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    position: relative;
    opacity: 0.7;
}

.mode-btn.disabled-btn:hover {
    background-color: #e9ecef;
    transform: none;
}

.mode-btn.full-width {
    width: 100%;
    max-width: 400px;
}

.coming-soon {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #ffc107;
    color: #212529;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    white-space: nowrap;
}

/* Responsive chess piece sizing on mobile */
@media screen and (max-width: 480px) {
    .chess-piece-large {
        font-size: 1.2em;
        margin: 0 8px;
    }

    .chess-piece-medium {
        font-size: 1.1em;
        margin: 0 4px;
    }
}

/* Mode Header and Back Button */
.mode-header {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
}

.back-btn {
    background-color: #5865f2;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background-color: #4752c4;
    transform: translateY(-1px);
}



/* Create/Join Game Sections */
.create-game-section, .join-game-section {
    margin-top: 20px;
    text-align: center;
}

.section-title {
    font-size: 1.1rem;
    margin-bottom: 15px;
    font-weight: 500;
    color: #333;
}

.connection-field {
    margin-bottom: 15px;
}

.game-code {
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    max-width: 400px;
    margin: 0 auto;
}

.code-input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: bold;
    text-align: center;
    letter-spacing: 2px;
    background-color: #f8f9fa;
    color: #333;
}

.code-input:focus {
    outline: none;
    border-color: #5865f2;
}

.connection-status {
    color: #666;
    font-size: 0.9rem;
    margin-top: 10px;
}

.input-with-paste {
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;
}

.input-with-paste .code-input {
    padding-right: 45px; /* Make space for the paste button */
    width: 100%;
}

.paste-btn-inline {
    position: absolute;
    right: 8px;
    background-color: #5865f2;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    opacity: 0.9;
    backdrop-filter: blur(2px);
}

.paste-btn-inline:hover {
    background-color: #4752c4;
    color: white;
    opacity: 1;
    transform: translateY(-1px);
}

.paste-btn-inline:active {
    background-color: #3c47b8;
    color: white;
    opacity: 1;
    transform: translateY(0) scale(0.95);
}

/* P2P Lobby */
.p2p-lobby {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 40px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    margin: 0 auto 30px;
}

.lobby-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 20px;
}

.lobby-header .back-btn {
    position: absolute;
    left: 0;
    z-index: 1001;
}

.lobby-title {
    margin: 0;
    color: #333;
    font-size: 1.8rem;
    flex: 1;
    text-align: center;
}

.connection-status {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 30px;
    min-height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.peer-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0;
    padding: 5px 0;
    width: 100%;
    font-weight: 500;
}

.status-indicator {
    font-size: 12px;
}

.status-indicator.connected {
    color: #28a745;
}

.status-indicator.connecting {
    color: #ffc107;
    animation: pulse 1.5s infinite;
}

.status-indicator.disconnected {
    color: #dc3545;
}

.peer-id-display,
.room-code-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: 'Courier New', monospace;
    background-color: #e9ecef;
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
}

.copy-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: color 0.3s;
}

.copy-btn:hover {
    color: #495057;
    background-color: #dee2e6;
}

.room-code-display {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    margin: 10px 0 !important;
}

.room-code-display #room-code {
    font-size: 24px;
    font-weight: bold;
    color: #1976d2;
    letter-spacing: 2px;
}

.lobby-options {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.option-section {
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: border-color 0.3s;
}

.option-section:hover {
    border-color: #5865f2;
}

.option-section h3 {
    margin-bottom: 10px;
    color: #333;
    font-size: 1.2rem;
}

.option-section p {
    margin-bottom: 20px;
    color: #666;
    font-size: 0.95rem;
}

.option-divider {
    font-weight: 600;
    color: #6c757d;
    position: relative;
    margin: 20px 0;
}

.option-divider::before,
.option-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background-color: #dee2e6;
}

.option-divider::before {
    left: 0;
}

.option-divider::after {
    right: 0;
}

.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    white-space: nowrap;
}

.action-btn.primary {
    background-color: #5865f2;
    color: white;
}

.action-btn.primary:hover {
    background-color: #4752c4;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background-color: #28a745;
    color: white;
}

.action-btn.secondary:hover {
    background-color: #218838;
    transform: translateY(-1px);
}

.action-btn.cancel {
    background-color: #dc3545;
    color: white;
}

.action-btn.cancel:hover {
    background-color: #c82333;
}

.join-form {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.join-form input {
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-size: 16px;
    min-width: 200px;
    text-align: center;
    font-family: 'Courier New', monospace;
}

.join-form input:focus {
    outline: none;
    border-color: #5865f2;
}

.connection-progress {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
}

.connection-message {
    margin: 20px 0;
    font-size: 16px;
    color: #666;
}

/* Matchmaking (legacy styles for compatibility) */
.matchmaking {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 40px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    margin: 0 auto 30px;
}

.matchmaking-title {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.8rem;
}

.matchmaking p {
    margin-bottom: 30px;
    color: #666;
    font-size: 1.1rem;
}

.matchmaking-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #5865f2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.matchmaking-message {
    font-size: 1.2rem;
    font-weight: 600;
    color: #5865f2;
}

.players-online {
    font-size: 1rem;
    color: #666;
    padding: 10px 20px;
    background-color: #f8f9fa;
    border-radius: 20px;
    border: 2px solid #e9ecef;
}

.players-online span {
    font-weight: 600;
    color: #27ae60;
}

.mode-btn {
    padding: 12px 25px;
    font-size: 1.1rem;
    min-width: 180px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.mode-btn.primary-btn {
    background-color: #5865f2;
    color: white;
}

.mode-btn.primary-btn:hover {
    background-color: #5865f2;
    transform: translateY(-2px);
}

.mode-btn.secondary-btn {
    background-color: #ecf0f1;
    color: #2c3e50;
}

.mode-btn.secondary-btn:hover {
    background-color: #d6e0e4;
    transform: translateY(-2px);
}

/* Footer Credit */
.footer-credit {
    margin-top: 20px;
    padding: 15px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-top: 2px solid #e9ecef;
}

.footer-credit p {
    margin: 0;
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.footer-credit a {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.footer-credit a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Mobile responsive for footer */
@media (max-width: 768px) {
    .footer-credit {
        margin-top: 15px;
        padding: 12px;
    }

    .footer-credit p {
        font-size: 0.85rem;
    }
}

/* Game Modes */
.single-player-mode, .multiplayer-mode {
    display: none;
}

/* Game Controls */
.game-controls {
    margin-bottom: 20px;
}

.game-settings {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    flex-wrap: wrap;
    gap: 15px;
}

.difficulty-selector {
    display: flex;
    align-items: center;
}

.difficulty-selector label {
    margin-right: 10px;
    font-weight: 500;
}

.difficulty-selector select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f8f9fa;
    font-family: 'Poppins', sans-serif;
}

.color-selector {
    display: flex;
    align-items: center;
}

.color-selector-title {
    margin-right: 10px;
    font-weight: 500;
}

.color-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
    cursor: pointer;
    margin-right: 5px;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.color-btn.active {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

/* Game Container */
.game-container, .mp-game-container {
    display: grid;
    grid-template-columns: minmax(320px, 650px) 300px;
    gap: 20px;
    margin-bottom: 30px;
}

.game-board-container, .mp-game-board-container {
    width: 100%;
    background-color: #ffffff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

#game-board, #mp-game-board {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    user-select: none;
    -webkit-user-select: none;
    touch-action: none;
}

.game-info, .mp-game-info {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Timer Container */
.timer-container {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.timer-section {
    flex: 1;
    text-align: center;
    background-color: #ffffff;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.timer-section.active {
    background-color: #3498db;
    color: white;
    transform: scale(1.02);
}

.timer-section.warning {
    background-color: #f39c12;
    color: white;
    animation: pulse 1s infinite;
}

.timer-section.danger {
    background-color: #e74c3c;
    color: white;
    animation: pulse 0.5s infinite;
}

.timer-label {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timer {
    font-size: 1.8rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Status Container */
.status-container, .mp-status-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.status-message, .mp-status-message {
    font-weight: 500;
    margin-bottom: 10px;
}

.turn-indicator {
    display: flex;
    gap: 10px;
}

.turn-player {
    flex: 1;
    padding: 8px 10px;
    border-radius: 5px;
    text-align: center;
    background-color: #eee;
    opacity: 0.6;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.turn-player.active {
    background-color: #3498db;
    color: white;
    opacity: 1;
}

.mp-player-info {
    display: flex;
    align-items: center;
    margin-top: 10px;
    background-color: #e6f7ff;
    padding: 10px;
    border-radius: 5px;
}

.mp-player-info-label {
    font-weight: 500;
    margin-right: 10px;
}

.mp-player-color {
    font-weight: 600;
    color: #3498db;
}

/* Captured Pieces */
.captured-pieces-container {
    margin-bottom: 20px;
}

.captured-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.captured-pieces {
    display: flex;
    flex-wrap: wrap;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    min-height: 40px;
}

.captured-piece {
    margin-right: 5px;
    font-size: 1.2rem;
}

/* Move List */
.move-list-container {
    margin-bottom: 20px;
}

.move-list-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.move-list {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    background-color: #f8f9fa;
}

.move-pair {
    display: grid;
    grid-template-columns: 40px 1fr 1fr;
    gap: 5px;
    margin-bottom: 5px;
    padding: 3px 0;
}

.move-pair:nth-child(odd) {
    background-color: #f0f0f0;
}

.move-number {
    font-weight: 500;
    color: #666;
}

.move-white, .move-black {
    font-family: 'Courier New', monospace;
}

/* Game Actions */
.game-actions {
    display: flex;
    gap: 10px;
}

.game-btn {
    padding: 10px 15px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
    text-align: center;
}

.primary-btn {
    background-color: #3498db;
    color: white;
}

.primary-btn:hover {
    background-color: #2980b9;
}

.secondary-btn {
    background-color: #ecf0f1;
    color: #34495e;
}

.secondary-btn:hover {
    background-color: #bdc3c7;
}

.danger-btn {
    background-color: #e74c3c;
    color: white;
}

.danger-btn:hover {
    background-color: #c0392b;
}

/* Chess board square highlighting */
.highlight-square {
    box-shadow: inset 0 0 0 4px rgba(88, 101, 242, 0.9) !important;
    position: relative;
    z-index: 10;
    background-color: rgba(88, 101, 242, 0.4) !important;
}

.highlight-target {
    box-shadow: inset 0 0 0 5px rgba(235, 69, 158, 0.9) !important;
    background-color: rgba(235, 69, 158, 0.3) !important;
    cursor: pointer;
    position: relative;
    z-index: 5;
}

.highlight-last-move {
    box-shadow: inset 0 0 0 3px rgba(80, 200, 120, 0.9) !important;
}

.highlight-castle {
    box-shadow: inset 0 0 0 3px rgba(255, 165, 0, 0.8) !important;
    background-color: rgba(255, 165, 0, 0.2) !important;
}

.highlight-check {
    box-shadow: inset 0 0 0 4px rgba(255, 0, 0, 0.9) !important;
    background-color: rgba(255, 0, 0, 0.3) !important;
    position: relative;
    z-index: 5;
}

/* Explicitly handle the mp-square-highlight class */
.mp-square-highlight {
    box-shadow: inset 0 0 0 3px rgba(80, 200, 120, 0.9) !important;
    background-color: rgba(80, 200, 120, 0.2) !important;
}

/* Make sure the pieces are above the highlight on selected squares */
.square-55d63 img {
    z-index: 20;
    position: relative;
}

/* Multiplayer Connection Setup */
.connection-setup {
    max-width: 500px;
    margin: 0 auto 30px;
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.setup-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    text-align: center;
}

.setup-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.create-game-section, .join-game-section {
    display: none;
    margin-top: 20px;
}

.section-title {
    font-size: 1.1rem;
    margin-bottom: 15px;
    font-weight: 500;
}

.connection-field {
    margin-bottom: 15px;
}

.game-code {
    display: flex;
    gap: 10px;
}

.code-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    text-transform: uppercase;
}

.connection-status {
    margin-top: 10px;
    padding: 1px;
    background-color: #f8f9fa;
    border-radius: 15px;
    font-weight: 500;
    color: #666;
}

.multiplayer-game {
    display: none;
}

/* Unicode chess pieces for captured display */
.piece-icon {
    font-size: 1.4rem;
    line-height: 1;
}

/* Footer styles */
.footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 30px 20px;
    width: 100%;
    margin-top: 50px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 30px;
}

.footer-logo h2 {
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.footer-links {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.link-group h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.link-group ul {
    list-style: none;
}

.link-group ul li {
    margin-bottom: 8px;
}

.link-group ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.link-group ul li a:hover {
    color: #3498db;
}

.link-group ul li a.active {
    color: #3498db;
    font-weight: 500;
}

.footer-bottom {
    max-width: 1200px;
    margin: 20px auto 0;
    padding-top: 20px;
    border-top: 1px solid #34495e;
    text-align: center;
    font-size: 0.9rem;
    color: #bdc3c7;
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
    .game-container, .mp-game-container {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 768px) {
    .game-title {
        font-size: 2rem;
    }
    
    .mode-buttons {
        flex-direction: column;
    }

    .button-row {
        flex-direction: column;
        width: 100%;
    }

    .mode-btn {
        width: 100%;
    }

    .coming-soon {
        top: -6px;
        right: -6px;
        font-size: 0.6rem;
        padding: 1px 4px;
    }
    
    .game-settings {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .difficulty-selector, .color-selector {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .setup-buttons {
        flex-direction: column;
    }
    
    .game-actions {
        flex-wrap: wrap;
    }
    
    .game-btn {
        flex: 1;
        min-width: 0;
        padding: 8px;
        font-size: 0.9rem;
    }

    .game-code {
        flex-direction: column;
        gap: 15px;
    }

    .code-input {
        font-size: 1rem;
        padding: 10px;
    }

    .paste-btn-inline {
        right: 6px;
        padding: 6px;
        font-size: 0.9rem;
    }

    .input-with-paste .code-input {
        padding-right: 40px;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
    }
    
    /* Increase the size of squares for better tapping */
    .square-55d63 {
        cursor: pointer;
    }
    
    /* Make highlights even more visible on mobile */
    .highlight-square {
        background-color: rgba(88, 101, 242, 0.4) !important;
    }
    
    .highlight-target {
        background-color: rgba(235, 69, 158, 0.3) !important;
    }
    
    /* Ensure there's no dragging feedback on mobile */
    body.dragging-piece {
        cursor: default !important;
    }
    
    /* Make tappable areas more obvious */
    .square-55d63:active {
        opacity: 0.7;
    }
    
    /* Increase touch targets for better mobile experience */
    .game-btn, .mode-btn, .color-btn {
        min-height: 44px;
        padding: 12px 15px;
    }
    
    /* Fix for chessboard sizing on mobile */
    #game-board, #mp-game-board {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
    }
    
    /* Ensure chess squares are sized properly */
    .square-55d63 {
        min-width: 40px !important;
        min-height: 40px !important;
        width: 12.5% !important;
        height: 0 !important;
        padding-bottom: 12.5% !important;
        position: relative !important;
    }
    
    /* Make sure chess pieces are properly positioned */
    .square-55d63 img {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
    }
    
    /* Improve tap feedback */
    .square-55d63:active {
        opacity: 0.8;
        transition: opacity 0.1s;
    }
    
    /* Disable text selection on mobile */
    .game-board-container, .mp-game-board-container {
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
    }
}

@media screen and (max-width: 480px) {
    .main-container {
        padding: 10px;
    }
    
    .game-board-container, .mp-game-board-container,
    .game-info, .mp-game-info {
        padding: 10px;
    }
    
    .connection-setup {
        padding: 15px;
    }
    
    .turn-player span {
        display: none;
    }
    
    .code-input {
        font-size: 0.9rem;
    }
    
    /* Further adjustments for very small screens */
    #game-board, #mp-game-board {
        margin: 0 auto !important;
    }
    
    /* Ensure full width for the chess board */
    .board-b72b1 {
        width: 100% !important;
    }

    /* Ensure proper spacing for the chess squares */
    .square-55d63 {
        float: left !important;
        position: relative !important;
    }
}

/* Responsive Chess Board - Additional fixes */
@media (max-width: 768px) {
    /* Force chessboard container to proper dimensions */
    .board-container {
        margin: 1rem auto;
        width: 100% !important;
    }
    
    #game-board, #mp-game-board {
        width: 100% !important;
        margin: 0 auto;
    }
    
    /* Fix for the chessboard.js library in mobile */
    .board-b72b1 {
        width: 100% !important;
        height: auto !important;
    }
}

@media (max-width: 480px) {
    .board-container {
        margin: 0.5rem auto;
    }
    
    /* Make the pieces better sized and clearer */
    .piece-417db {
        transform: scale(1.1);
    }
    
    /* Ensure chess board container is properly sized */
    .game-container, .mp-game-container {
        grid-template-columns: 1fr;
    }
    
    .game-info, .mp-game-info {
        margin-top: 20px;
    }
    
    .captured-pieces-container, .move-list-container {
        max-height: 150px;
        overflow-y: auto;
    }
    
    /* Adjust the header for mobile */
    .game-title {
        font-size: 1.8rem;
        margin: 15px 0;
    }
}

/* Make sure the chess pieces are above other elements during drag */
img.piece-417db {
    z-index: 100;
    pointer-events: auto !important;
}

/* Fix ghost pieces during drag */
body > img[src*="chesspieces"] {
    z-index: 9999;
    pointer-events: none !important;
}

/* Game Over and Promotion Popup Styles */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease-out;
}

/* Special case for promotion popup which is positioned absolutely */
#promotion-popup.popup-overlay {
    position: absolute;
    top: auto;
    left: auto;
    width: auto;
    height: auto;
    background-color: white;
    backdrop-filter: none;
}

.popup-overlay.show {
    display: flex;
}

.popup-content {
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Promotion Popup Styles */
#promotion-popup {
    position: absolute;
    background-color: white;
    color: #2c3e50;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5), 0 0 0 1000px rgba(0, 0, 0, 0.5);
    z-index: 10000;
    min-width: 300px;
    padding: 25px;
    text-align: center;
    animation: scaleIn 0.3s ease-out;
    border: 3px solid #4a69bd;
}

.promotion-title {
    font-size: 1.6rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 20px;
    color: #2c3e50;
    padding: 10px 0;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.promotion-options {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    flex-wrap: wrap;
    gap: 20px;
}

.promotion-piece {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 15px;
    border-radius: 10px;
    transition: all 0.2s ease;
    background-color: rgba(0, 0, 0, 0.05);
    width: 100px;
    margin: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 2px solid transparent;
}

.promotion-piece:hover {
    background-color: rgba(74, 105, 189, 0.1);
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-color: #4a69bd;
}

.promotion-piece-img {
    width: 80px;
    height: 80px;
    margin-bottom: 15px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
}

.promotion-piece-name {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* White pieces */
.wQ {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/wQ.png');
}

.wR {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/wR.png');
}

.wB {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/wB.png');
}

.wN {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/wN.png');
}

/* Black pieces */
.bQ {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/bQ.png');
}

.bR {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/bR.png');
}

.bB {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/bB.png');
}

.bN {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/bN.png');
}

.black-promotion .promotion-piece[data-piece="r"] .piece-icon {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/bR.png');
}

.black-promotion .promotion-piece[data-piece="b"] .piece-icon {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/bB.png');
}

.black-promotion .promotion-piece[data-piece="n"] .piece-icon {
    background-image: url('https://chessboardjs.com/img/chesspieces/alpha/bN.png');
}

.popup-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 450px;
    width: 90%;
    position: relative;
    animation: slideIn 0.4s ease-out;
    color: white;
    overflow: hidden;
}

.popup-body {
    padding: 40px 30px;
    text-align: center;
}

.popup-body h2 {
    font-size: 28px;
    font-weight: 700;
    margin: 20px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.result-icon {
    margin-bottom: 20px;
}

.result-icon i {
    font-size: 80px;
    color: #ffd700;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: bounce 1s ease-in-out;
}

.result-icon.checkmate i {
    color: #ffd700;
}

.result-icon.stalemate i {
    color: #95a5a6;
}

.result-icon.draw i {
    color: #3498db;
}

.result-icon.resignation i {
    color: #e74c3c;
}

#game-result-message {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.game-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 500;
    opacity: 0.9;
}

.stat-item span:last-child {
    font-weight: 600;
    color: #ffd700;
}



/* Turn indicator */
.turn-indicator {
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    margin-top: 10px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

.turn-indicator.my-turn {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
    animation: pulse 2s infinite;
}

.turn-indicator.opponent-turn {
    background: linear-gradient(45deg, #95a5a6, #bdc3c7);
    color: white;
    box-shadow: 0 2px 8px rgba(149, 165, 166, 0.3);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(46, 204, 113, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Mobile responsive styles for popup */
@media (max-width: 768px) {
    .popup-content {
        margin: 20px;
        max-width: calc(100vw - 40px);
    }

    .popup-body h2 {
        font-size: 24px;
    }

    .result-icon i {
        font-size: 60px;
    }

    #game-result-message {
        font-size: 18px;
    }

    .popup-body {
        padding: 30px 25px;
    }

    .game-stats {
        padding: 15px;
    }

    /* P2P Lobby Mobile Styles */
    .p2p-lobby {
        padding: 20px;
        margin: 10px;
    }

    .lobby-title {
        font-size: 1.5rem;
    }

    .lobby-options {
        gap: 20px;
    }

    .option-section {
        padding: 15px;
    }

    .join-form {
        flex-direction: column;
        gap: 15px;
    }

    .join-form input {
        min-width: 100%;
        width: 100%;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }

    .peer-id-display,
    .room-code-display {
        flex-direction: column;
        gap: 10px;
        font-size: 12px;
    }

    .connection-status {
        padding: 15px;
    }
}