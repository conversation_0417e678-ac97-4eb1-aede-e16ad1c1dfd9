const express = require('express');
const path = require('path');

const app = express();

// Serve static files from the current directory
app.use(express.static(path.join(__dirname)));

// Serve chess.html as the default page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'chess.html'));
});

// Note: This server is now optional since the chess game is fully P2P
// You can also serve the files using any static file server like:
// python -m http.server 8000
// npx http-server
// php -S localhost:8000

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Static file server running on port ${PORT}`);
  console.log(`Open http://localhost:${PORT} to play P2P Chess`);
});


